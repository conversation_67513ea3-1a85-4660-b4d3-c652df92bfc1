{% extends "financial/base.html" %}

{% block title %}查看财务凭证{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">财务凭证详情</h3>
                    <div class="card-tools">
                        {% if voucher.status in ['草稿', '待审核'] %}
                        <div class="btn-group">
                            <button type="button" class="btn btn-warning btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="{{ url_for('financial.edit_voucher_professional', id=voucher.id) }}">
                                    <i class="fas fa-magic"></i> 专业编辑器
                                </a>
                                <a class="dropdown-item" href="{{ url_for('financial.edit_voucher', id=voucher.id) }}">
                                    <i class="fas fa-edit"></i> 标准编辑器
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        {% if voucher.status == '待审核' %}
                        <form method="POST" action="{{ url_for('financial.review_voucher', id=voucher.id) }}" 
                              style="display: inline;" onsubmit="return confirm('确定要审核此凭证吗？')">
        {{ csrf_token() }}
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-check"></i> 审核
                            </button>
                        </form>
                        {% endif %}
                        <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-file-alt"></i> 文本视图
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 凭证基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">凭证号：</th>
                                    <td>{{ voucher.voucher_number }}</td>
                                </tr>
                                <tr>
                                    <th>凭证日期：</th>
                                    <td>{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>凭证类型：</th>
                                    <td>{{ voucher.voucher_type }}</td>
                                </tr>
                                <tr>
                                    <th>状态：</th>
                                    <td>
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% else %}
                                            <span class="badge badge-light">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">总金额：</th>
                                    <td class="text-end fw-bold">{{ "%.2f"|format(voucher.total_amount) }}</td>
                                </tr>
                                <tr>
                                    <th>创建人：</th>
                                    <td>{{ voucher.creator.username if voucher.creator else '未知' }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间：</th>
                                    <td>{{ voucher.created_at.strftime('%Y-%m-%d %H:%M') if voucher.created_at else '未知' }}</td>
                                </tr>
                                {% if voucher.reviewed_by %}
                                <tr>
                                    <th>审核人：</th>
                                    <td>{{ voucher.reviewer.username if voucher.reviewer else '未知' }}</td>
                                </tr>
                                <tr>
                                    <th>审核时间：</th>
                                    <td>{{ voucher.reviewed_at.strftime('%Y-%m-%d %H:%M') if voucher.reviewed_at else '未知' }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5>摘要</h5>
                            <p class="border p-3">{{ voucher.summary or '无' }}</p>
                        </div>
                    </div>
                    
                    {% if voucher.notes %}
                    <div class="row">
                        <div class="col-md-12">
                            <h5>备注</h5>
                            <p class="border p-3">{{ voucher.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 凭证明细 -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>凭证明细</h5>
                            {% if details %}
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>行号</th>
                                            <th>会计科目</th>
                                            <th>摘要</th>
                                            <th class="text-end">借方金额</th>
                                            <th class="text-end">贷方金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for detail in details %}
                                        <tr>
                                            <td>{{ detail.line_number }}</td>
                                            <td>{{ detail.accounting_subject.code }} - {{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</td>
                                            <td>{{ detail.summary or '' }}</td>
                                            <td class="text-end">{{ "%.2f"|format(detail.debit_amount) if detail.debit_amount > 0 else '' }}</td>
                                            <td class="text-end">{{ "%.2f"|format(detail.credit_amount) if detail.credit_amount > 0 else '' }}</td>
                                        </tr>
                                        {% endfor %}
                                        <tr class="fw-bold">
                                            <td colspan="3" class="text-end">合计：</td>
                                            <td class="text-end">{{ "%.2f"|format(details|sum(attribute='debit_amount')) }}</td>
                                            <td class="text-end">{{ "%.2f"|format(details|sum(attribute='credit_amount')) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 暂无凭证明细
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
