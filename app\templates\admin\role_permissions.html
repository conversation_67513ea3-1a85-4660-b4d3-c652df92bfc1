{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.permission_help') }}" target="_blank" class="btn btn-info">
            <i class="fas fa-question-circle"></i> 权限配置帮助
        </a>
        <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回角色详情
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">角色信息</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>角色名称：</strong> {{ role.name }}</p>
                <p><strong>角色描述：</strong> {{ role.description or '无' }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>创建时间：</strong> {{  role.created_at|format_datetime  }}</p>
                <p><strong>关联用户数：</strong> {{ role.users.count() }}</p>
            </div>
        </div>
    </div>
</div>

<form method="POST" action="{{ url_for('system.edit_role_permissions', id=role.id) }}">
    {{ form.csrf_token }}

    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 class="mb-0">权限配置</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" id="selectAll">全选</button>
                    <button type="button" class="btn btn-sm btn-secondary" id="deselectAll">取消全选</button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                        </div>
                        <input type="text" class="form-control" id="permissionSearch" placeholder="搜索权限...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                            <span class="input-group-text">筛选模块</span>
                        </div>
                        <select class="form-control" id="moduleFilter">
                            <option value="all">所有模块</option>
                            {% for module in permissions %}
                            <option value="{{ module.module }}">{{ module.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <p><i class="fas fa-info-circle"></i> 提示：</p>
                <ul>
                    <li>选择模块下的"所有操作"将授予该模块的所有权限</li>
                    <li>选择"全局权限"下的"所有操作"将授予系统的所有权限</li>
                    <li>点击右上角的"权限配置帮助"按钮，查看详细的权限说明</li>
                </ul>
            </div>

            <div class="alert alert-warning">
                <p><i class="fas fa-exclamation-triangle"></i> 模块变化说明：</p>
                <ul>
                    <li><span class="badge badge-warning">已弃用</span> 标记的模块已被新模块替代，仅保留用于兼容性</li>
                    <li><span class="badge badge-info">已合并</span> 标记的模块已与其他模块合并，仅保留用于兼容性</li>
                    <li><span class="badge badge-success">新增</span> 标记的模块是新增的功能模块</li>
                </ul>
                <p>建议更新：</p>
                <ul>
                    <li>如果角色有"菜单计划管理"权限，建议同时授予"周菜单管理"权限</li>
                    <li>如果角色有"留样管理"权限，建议同时授予"食材溯源与留样"权限</li>
                    <li>对于食堂管理员角色，建议授予"食堂日常管理"的所有权限</li>
                </ul>
            </div>

            <div class="row">
                {% for module in permissions %}
                <div class="col-md-6 mb-4 permission-card" data-module="{{ module.module }}">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input type="checkbox" class="custom-control-input module-checkbox" id="module_{{ module.module }}" data-module="{{ module.module }}">
                                    <label class="form-check-label" for="module_{{ module.module }}">
                                        <strong>{{ module.name }}</strong>
                                        {% if module.module == 'menu_plan' %}
                                        <span class="badge badge-warning">已弃用</span>
                                        {% elif module.module == 'sample' %}
                                        <span class="badge badge-info">已合并</span>
                                        {% elif module.module == 'weekly_menu' %}
                                        <span class="badge badge-success">新增</span>
                                        {% elif module.module == 'traceability' %}
                                        <span class="badge badge-success">新增</span>
                                        {% elif module.module == 'daily_management' %}
                                        <span class="badge badge-success">新增</span>
                                        {% endif %}
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-info module-info-btn" data-bs-toggle="collapse" data-bs-target="#module_info_{{ module.module }}">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="collapse" id="module_info_{{ module.module }}">
                            <div class="card-body bg-light">
                                {% if module.description %}
                                <p><strong>描述：</strong> {{ module.description }}</p>
                                {% endif %}
                                {% if module.usage_scenarios %}
                                <p><strong>使用场景：</strong> {{ module.usage_scenarios }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="permissions-list">
                                {% for action in module.actions %}
                                <div class="custom-control custom-checkbox permission-item" data-action-name="{{ action.name }}">
                                    <input type="checkbox" class="custom-control-input permission-checkbox"
                                           id="perm_{{ module.module }}_{{ action.action }}"
                                           name="permissions"
                                           value="{{ module.module }}:{{ action.action }}"
                                           data-module="{{ module.module }}"
                                           {% if module.module in current_permissions and action.action in current_permissions[module.module] %}checked{% endif %}>
                                    <label class="form-check-label" for="perm_{{ module.module }}_{{ action.action }}">
                                        {{ module.module | module_chinese_name }} - {{ (module.module, action.action) | permission_chinese_name }}
                                        {% if action.description or action.impact %}
                                        <a href="#" class="action-info-btn" data-bs-toggle="popover" data-html="true"
                                           title="{{ action.name }}"
                                           data-content="{% if action.description %}<p><strong>描述：</strong> {{ action.description }}</p>{% endif %}{% if action.impact %}<p><strong>影响：</strong> {{ action.impact }}</p>{% endif %}{% if action.typical_roles %}<p><strong>典型角色：</strong> {{  action.typical_roles|join(', ')  }}</p>{% endif %}">
                                            <i class="fas fa-question-circle text-info"></i>
                                        </a>
                                        {% endif %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">保存权限设置</button>
            <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-secondary">取消</a>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 初始化弹出框
    $('[data-bs-toggle="popover"]').popover({
        trigger: 'hover',
        placement: 'auto'
    });

    // 模块选择框点击事件
    $('.module-checkbox').change(function() {
        var module = $(this).data('module');
        var checked = $(this).prop('checked');

        // 选中/取消选中该模块下的所有权限
        $('input[data-module="' + module + '"].permission-checkbox').prop('checked', checked);
    });

    // 权限选择框点击事件
    $('.permission-checkbox').change(function() {
        var module = $(this).data('module');
        var allChecked = true;

        // 检查该模块下的所有权限是否都被选中
        $('input[data-module="' + module + '"].permission-checkbox').each(function() {
            if (!$(this).prop('checked')) {
                allChecked = false;
                return false;
            }
        });

        // 更新模块选择框状态
        $('#module_' + module).prop('checked', allChecked);
    });

    // 初始化模块选择框状态
    $('.module-checkbox').each(function() {
        var module = $(this).data('module');
        var allChecked = true;

        // 检查该模块下的所有权限是否都被选中
        $('input[data-module="' + module + '"].permission-checkbox').each(function() {
            if (!$(this).prop('checked')) {
                allChecked = false;
                return false;
            }
        });

        // 更新模块选择框状态
        $(this).prop('checked', allChecked);
    });

    // 全选按钮
    $('#selectAll').click(function() {
        $('.permission-checkbox:visible, .module-checkbox:visible').prop('checked', true);
    });

    // 取消全选按钮
    $('#deselectAll').click(function() {
        $('.permission-checkbox:visible, .module-checkbox:visible').prop('checked', false);
    });

    // 搜索权限
    $('#permissionSearch').on('input', function() {
        var searchText = $(this).val().toLowerCase();
        var moduleFilter = $('#moduleFilter').val();

        // 遍历所有权限项
        $('.permission-card').each(function() {
            var moduleId = $(this).data('module');
            var showModule = false;

            // 如果模块筛选不是"all"，且不匹配当前模块，则隐藏整个模块
            if (moduleFilter !== 'all' && moduleFilter !== moduleId) {
                $(this).hide();
                return;
            }

            // 遍历该模块下的所有权限项
            $(this).find('.permission-item').each(function() {
                var actionName = $(this).data('action-name').toLowerCase();

                // 如果权限名称包含搜索文本，则显示该权限项
                if (actionName.indexOf(searchText) > -1) {
                    $(this).show();
                    showModule = true;
                } else {
                    $(this).hide();
                }
            });

            // 根据是否有匹配的权限项来决定是否显示该模块
            if (showModule) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // 模块筛选
    $('#moduleFilter').change(function() {
        // 触发搜索，以便同时应用搜索和筛选条件
        $('#permissionSearch').trigger('input');
    });
});
</script>
{% endblock %}
