{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .btn-outline-primary.active,
    .btn-outline-info.active,
    .btn-outline-success.active,
    .btn-outline-warning.active,
    .btn-outline-danger.active,
    .btn-outline-secondary.active,
    .btn-outline-dark.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    #backToTop {
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    #backToTop:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .list-group-item {
        transition: background-color 0.2s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .alert {
        border-start: 4px solid;
    }

    .alert-info {
        border-start-color: #17a2b8;
    }

    .alert-success {
        border-start-color: #28a745;
    }

    .alert-warning {
        border-start-color: #ffc107;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        学校食堂管理系统 - 使用帮助文档
                    </h1>
                    <p class="mb-0 mt-2">全面的系统功能介绍和操作指南</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-compass me-2"></i>快速导航</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#system-overview" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-home me-1"></i>系统概述
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#core-modules" class="btn btn-outline-info btn-sm w-100">
                                <i class="fas fa-cubes me-1"></i>核心模块
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#daily-management" class="btn btn-outline-success btn-sm w-100">
                                <i class="fas fa-calendar-day me-1"></i>日常管理
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#user-roles" class="btn btn-outline-warning btn-sm w-100">
                                <i class="fas fa-users me-1"></i>用户角色
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#advanced-features" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-cogs me-1"></i>高级功能
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#operation-guide" class="btn btn-outline-secondary btn-sm w-100">
                                <i class="fas fa-book me-1"></i>操作指南
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#faq" class="btn btn-outline-dark btn-sm w-100">
                                <i class="fas fa-question me-1"></i>常见问题
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <a href="#contact" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-phone me-1"></i>技术支持
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统概述 -->
    <div class="row mb-4" id="system-overview">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fas fa-home me-2"></i>系统概述</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>学校食堂管理系统简介</h5>
                            <p>本系统是专为学校食堂设计的综合管理平台，涵盖从食材采购到餐桌服务的全流程管理。系统采用现代化的Web技术，支持多终端访问，确保食品安全和营养健康。</p>
                            
                            <h6>核心特色</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i><strong>全流程管理：</strong>从供应商管理到食材溯源的完整链条</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>数据隔离：</strong>严格的学校级数据隔离，确保数据安全</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>移动友好：</strong>支持手机、平板等移动设备操作</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>智能分析：</strong>提供数据统计和趋势分析</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>合规管理：</strong>符合食品安全法规要求</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-utensils fa-3x text-primary mb-3"></i>
                                    <h6>系统版本</h6>
                                    <p class="mb-1">学校食堂管理系统</p>
                                    <p class="mb-1">Version 2.0</p>
                                    <small class="text-muted">专业版</small>

                                    <hr class="my-3">
                                    <h6>技术架构</h6>
                                    <ul class="small mb-0 text-start">
                                        <li>Flask Web框架</li>
                                        <li>SQL Server数据库</li>
                                        <li>Bootstrap响应式UI</li>
                                        <li>RESTful API设计</li>
                                        <li>JWT安全认证</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心模块 -->
    <div class="row mb-4" id="core-modules">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-cubes me-2"></i>核心功能模块</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 供应商管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-truck me-2"></i>供应商管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">建立合格供应商档案，确保食材来源可靠</p>
                                    <ul class="small mb-0">
                                        <li>供应商基础信息管理</li>
                                        <li>资质证书管理</li>
                                        <li>供应商评价体系</li>
                                        <li>供应商产品目录</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：供应商管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 食材管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-info h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-leaf me-2"></i>食材管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">建立完整的食材基础档案和营养信息</p>
                                    <ul class="small mb-0">
                                        <li>食材基础信息</li>
                                        <li>营养成分管理</li>
                                        <li>食材分类体系</li>
                                        <li>价格参考管理</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：食材管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 食谱管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-warning h-100">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0"><i class="fas fa-utensils me-2"></i>食谱管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">制定营养均衡的食谱，自动计算成本</p>
                                    <ul class="small mb-0">
                                        <li>食谱制作工艺</li>
                                        <li>营养成分计算</li>
                                        <li>成本自动核算</li>
                                        <li>食谱图片管理</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：食谱管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 周菜单制定 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>周菜单制定</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">科学制定周菜单，营养搭配合理</p>
                                    <ul class="small mb-0">
                                        <li>拖拽式菜品安排</li>
                                        <li>营养分析报告</li>
                                        <li>成本控制管理</li>
                                        <li>一键生成采购单</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：周菜单管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 采购管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-danger h-100">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>采购管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">规范采购流程，确保食材质量</p>
                                    <ul class="small mb-0">
                                        <li>采购订单管理</li>
                                        <li>供应商选择</li>
                                        <li>价格比较分析</li>
                                        <li>采购记录追踪</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：采购订单管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 库存管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-secondary h-100">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0"><i class="fas fa-warehouse me-2"></i>库存管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">精确管理食材库存，避免浪费</p>
                                    <ul class="small mb-0">
                                        <li>入库出库管理</li>
                                        <li>库存预警提醒</li>
                                        <li>先进先出原则</li>
                                        <li>盘点差异处理</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：库存管理</small>
                                </div>
                            </div>
                        </div>

                        <!-- 消耗量计划 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-dark h-100">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>消耗量计划</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">制定食材使用计划，精确控制用量</p>
                                    <ul class="small mb-0">
                                        <li>基于菜单计算用量</li>
                                        <li>智能推荐消耗量</li>
                                        <li>库存余量检查</li>
                                        <li>自动生成出库单</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：消耗量计划</small>
                                </div>
                            </div>
                        </div>

                        <!-- 食材溯源 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-search-plus me-2"></i>食材溯源</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">从供应商到餐桌的全链路追踪</p>
                                    <ul class="small mb-0">
                                        <li>批次管理追踪</li>
                                        <li>流向记录查询</li>
                                        <li>问题快速定位</li>
                                        <li>溯源报告生成</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：食材溯源</small>
                                </div>
                            </div>
                        </div>

                        <!-- 留样管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-vial me-2"></i>留样管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">规范的食品留样管理</p>
                                    <ul class="small mb-0">
                                        <li>留样记录管理</li>
                                        <li>保存期限提醒</li>
                                        <li>照片证据上传</li>
                                        <li>一键生成功能</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">访问路径：留样管理</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>系统特色</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>学校级数据隔离：</strong>每个学校只能访问自己的数据，确保数据安全
                            </div>
                            <div class="col-md-4">
                                <strong>全流程管理：</strong>从供应商管理到食材溯源的完整业务链条
                            </div>
                            <div class="col-md-4">
                                <strong>智能化分析：</strong>提供成本分析、营养分析等智能报表
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理功能模块 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0"><i class="fas fa-cog me-2"></i>管理功能模块</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 用户管理 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-users-cog me-2"></i>用户管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">系统用户账号管理</p>
                                    <ul class="small mb-0">
                                        <li>用户创建编辑</li>
                                        <li>角色权限分配</li>
                                        <li>密码重置管理</li>
                                        <li>用户状态控制</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 区域管理 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-info h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-map-marked-alt me-2"></i>区域管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">行政区域层级管理</p>
                                    <ul class="small mb-0">
                                        <li>县市区管理</li>
                                        <li>乡镇街道管理</li>
                                        <li>学校信息管理</li>
                                        <li>层级关系维护</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 仓库管理 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-warning h-100">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0"><i class="fas fa-warehouse me-2"></i>仓库管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">仓库和储位管理</p>
                                    <ul class="small mb-0">
                                        <li>仓库基础信息</li>
                                        <li>储位分区管理</li>
                                        <li>温度环境设置</li>
                                        <li>容量限制管理</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 系统设置 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-danger h-100">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>系统设置</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">系统参数配置</p>
                                    <ul class="small mb-0">
                                        <li>系统参数设置</li>
                                        <li>数据备份恢复</li>
                                        <li>安全策略配置</li>
                                        <li>日志监控管理</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日常管理模块 -->
    <div class="row mb-4" id="daily-management">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0"><i class="fas fa-calendar-day me-2"></i>食堂日常管理模块</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-star me-2"></i>核心亮点</h6>
                        <p class="mb-0">食堂日常管理是系统的核心模块，包含6个子功能，支持二维码扫描上传和PDF报告生成。</p>
                    </div>

                    <div class="row">
                        <!-- 检查记录 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>检查记录</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">食品安全检查，支持二维码扫描上传</p>
                                    <ul class="small mb-0">
                                        <li>每日安全检查</li>
                                        <li>二维码扫描上传</li>
                                        <li>照片证据管理</li>
                                        <li>检查结果统计</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 陪餐记录 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>陪餐记录</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">陪餐人员记录，生成陪餐报告</p>
                                    <ul class="small mb-0">
                                        <li>陪餐人员登记</li>
                                        <li>用餐时间记录</li>
                                        <li>陪餐评价反馈</li>
                                        <li>陪餐统计分析</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 培训记录 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>培训记录</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">员工培训档案管理</p>
                                    <ul class="small mb-0">
                                        <li>培训计划制定</li>
                                        <li>培训内容记录</li>
                                        <li>培训效果评估</li>
                                        <li>培训证书管理</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 特殊事件 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>特殊事件</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">突发事件记录处理</p>
                                    <ul class="small mb-0">
                                        <li>事件详情记录</li>
                                        <li>处理过程跟踪</li>
                                        <li>责任人员确认</li>
                                        <li>事件分析总结</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 问题记录 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-bug me-2"></i>问题记录</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">问题发现与整改跟踪</p>
                                    <ul class="small mb-0">
                                        <li>问题发现登记</li>
                                        <li>整改措施制定</li>
                                        <li>整改进度跟踪</li>
                                        <li>整改效果验证</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 工作日志 -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white py-2">
                                    <h6 class="mb-0"><i class="fas fa-calendar-check me-2"></i>工作日志</h6>
                                </div>
                                <div class="card-body py-2">
                                    <p class="small mb-2">日常工作记录，生成工作报告</p>
                                    <ul class="small mb-0">
                                        <li>每日工作记录</li>
                                        <li>工作内容详情</li>
                                        <li>工作时间统计</li>
                                        <li>工作报告生成</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-lightbulb me-2"></i>特色功能</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>二维码检查：</strong>生成学校专属二维码，员工扫码上传检查数据
                            </div>
                            <div class="col-md-4">
                                <strong>PDF报告：</strong>一键生成专业PDF报告，方便各部门检查
                            </div>
                            <div class="col-md-4">
                                <strong>资料整合：</strong>整合相关资料，形成完整的管理档案
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户角色权限 -->
    <div class="row mb-4" id="user-roles">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="fas fa-users me-2"></i>用户角色与权限</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 系统管理员 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-danger h-100">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-crown me-2"></i>系统管理员</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">拥有系统最高权限</p>
                                    <ul class="small mb-0">
                                        <li>所有模块完全访问</li>
                                        <li>用户管理权限</li>
                                        <li>系统设置权限</li>
                                        <li>数据备份恢复</li>
                                        <li>超级删除功能</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 超级管理员 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-warning h-100">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0"><i class="fas fa-user-shield me-2"></i>超级管理员</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">教育局和乡镇官员</p>
                                    <ul class="small mb-0">
                                        <li>业务模块完全访问</li>
                                        <li>用户创建编辑</li>
                                        <li>区域管理权限</li>
                                        <li>数据查看权限</li>
                                        <li>报告生成权限</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 学校管理员 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-school me-2"></i>学校管理员</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">学校食堂负责人</p>
                                    <ul class="small mb-0">
                                        <li>本校数据完全访问</li>
                                        <li>日常管理权限</li>
                                        <li>菜单制定权限</li>
                                        <li>采购管理权限</li>
                                        <li>报告查看权限</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 普通用户 -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card border-info h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>普通用户</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">食堂工作人员</p>
                                    <ul class="small mb-0">
                                        <li>基础数据查看</li>
                                        <li>日常记录录入</li>
                                        <li>库存操作权限</li>
                                        <li>检查记录上传</li>
                                        <li>个人信息管理</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-shield-alt me-2"></i>数据安全保障</h6>
                        <p class="mb-0">系统采用严格的学校级数据隔离机制，确保每个学校只能访问自己的数据，保障数据安全和隐私。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 高级功能 -->
    <div class="row mb-4" id="advanced-features">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0"><i class="fas fa-cogs me-2"></i>高级功能</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 食材溯源 -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-search-plus me-2"></i>食材溯源系统</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">从供应商到餐桌的全链路追踪</p>
                                    <ul class="small">
                                        <li><strong>批次管理：</strong>每批食材都有唯一批次号</li>
                                        <li><strong>流向追踪：</strong>记录食材从入库到使用的完整流程</li>
                                        <li><strong>问题定位：</strong>快速定位问题食材的来源和去向</li>
                                        <li><strong>报告生成：</strong>生成详细的溯源报告</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 留样管理 -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-vial me-2"></i>留样管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">规范的食品留样管理</p>
                                    <ul class="small">
                                        <li><strong>自动提醒：</strong>系统自动提醒留样时间</li>
                                        <li><strong>标准流程：</strong>按照食品安全法规执行</li>
                                        <li><strong>照片记录：</strong>留样过程照片证据</li>
                                        <li><strong>保存期限：</strong>自动计算留样保存期限</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 数据分析 -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>数据分析</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">智能数据分析和报表</p>
                                    <ul class="small">
                                        <li><strong>成本分析：</strong>食材成本趋势分析</li>
                                        <li><strong>营养分析：</strong>营养成分统计报告</li>
                                        <li><strong>库存分析：</strong>库存周转率分析</li>
                                        <li><strong>供应商分析：</strong>供应商绩效评估</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端支持 -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0"><i class="fas fa-mobile-alt me-2"></i>移动端支持</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">全面的移动端功能支持</p>
                                    <ul class="small">
                                        <li><strong>响应式设计：</strong>适配各种屏幕尺寸</li>
                                        <li><strong>二维码扫描：</strong>手机扫码快速录入</li>
                                        <li><strong>照片上传：</strong>现场拍照即时上传</li>
                                        <li><strong>离线功能：</strong>支持离线数据录入</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作指南 -->
    <div class="row mb-4" id="operation-guide">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0"><i class="fas fa-book me-2"></i>操作指南</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>新用户入门</h5>
                            <ol class="small">
                                <li><strong>注册登录：</strong>使用学校信息注册账号</li>
                                <li><strong>完善信息：</strong>补充学校基础信息</li>
                                <li><strong>添加供应商：</strong>建立供应商档案</li>
                                <li><strong>录入食材：</strong>建立食材基础数据</li>
                                <li><strong>制定食谱：</strong>创建营养均衡的食谱</li>
                                <li><strong>安排菜单：</strong>制定周菜单计划</li>
                                <li><strong>开始使用：</strong>进入日常管理流程</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>日常操作流程</h5>
                            <ol class="small">
                                <li><strong>制定周菜单：</strong>每周制定下周菜单</li>
                                <li><strong>生成采购单：</strong>根据菜单自动生成</li>
                                <li><strong>采购食材：</strong>按采购单采购食材</li>
                                <li><strong>食材入库：</strong>验收合格食材入库</li>
                                <li><strong>制作菜品：</strong>按食谱制作菜品</li>
                                <li><strong>留样记录：</strong>按规定进行留样</li>
                                <li><strong>日常检查：</strong>记录各项检查结果</li>
                            </ol>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-lightbulb me-2"></i>操作提示</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>数据备份：</strong>定期备份重要数据
                            </div>
                            <div class="col-md-4">
                                <strong>权限管理：</strong>合理分配用户权限
                            </div>
                            <div class="col-md-4">
                                <strong>定期检查：</strong>定期检查系统运行状态
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 常见问题 -->
    <div class="row mb-4" id="faq">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fas fa-question me-2"></i>常见问题</h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <!-- 问题1 -->
                        <div class="card">
                            <div class="card-header" id="faq1">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                        <i class="fas fa-question-circle me-2"></i>如何重置密码？
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body small">
                                    <p>如果忘记密码，可以通过以下方式重置：</p>
                                    <ol>
                                        <li>联系系统管理员或上级管理员</li>
                                        <li>提供用户名和身份验证信息</li>
                                        <li>管理员在用户管理中重置密码</li>
                                        <li>使用新密码登录后及时修改</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- 问题2 -->
                        <div class="card">
                            <div class="card-header" id="faq2">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                        <i class="fas fa-question-circle me-2"></i>如何上传照片？
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body small">
                                    <p>系统支持多种照片上传方式：</p>
                                    <ol>
                                        <li><strong>网页上传：</strong>在相应页面点击上传按钮</li>
                                        <li><strong>二维码扫描：</strong>扫描二维码后拍照上传</li>
                                        <li><strong>移动端：</strong>使用手机直接拍照上传</li>
                                        <li><strong>批量上传：</strong>支持一次上传多张照片</li>
                                    </ol>
                                    <p class="text-muted">注意：照片会自动压缩到800x600像素以节省存储空间。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 问题3 -->
                        <div class="card">
                            <div class="card-header" id="faq3">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                        <i class="fas fa-question-circle me-2"></i>数据安全如何保障？
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse3" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body small">
                                    <p>系统采用多重安全保障措施：</p>
                                    <ul>
                                        <li><strong>数据隔离：</strong>严格的学校级数据隔离</li>
                                        <li><strong>权限控制：</strong>基于角色的权限管理</li>
                                        <li><strong>数据加密：</strong>敏感数据加密存储</li>
                                        <li><strong>操作日志：</strong>完整的操作审计日志</li>
                                        <li><strong>定期备份：</strong>自动数据备份机制</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 技术支持 -->
    <div class="row mb-4" id="contact">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-phone me-2"></i>技术支持</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>联系方式</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <p class="mb-2"><i class="fas fa-phone text-primary me-2"></i><strong>技术热线：</strong>18373062333</p>
                                    <p class="mb-2"><i class="fas fa-envelope text-primary me-2"></i><strong>邮箱支持：</strong><EMAIL></p>
                                    <p class="mb-2"><i class="fas fa-clock text-primary me-2"></i><strong>服务时间：</strong>周一至周五 9:00-18:00</p>
                                    <p class="mb-0"><i class="fas fa-globe text-primary me-2"></i><strong>在线支持：</strong>系统内置在线咨询功能</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>获取帮助</h5>
                            <div class="list-group">
                                <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="alert('功能开发中，敬请期待！')">
                                    <i class="fas fa-video me-2"></i>观看操作演示视频
                                </a>
                                <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="alert('功能开发中，敬请期待！')">
                                    <i class="fas fa-file-pdf me-2"></i>下载用户手册PDF
                                </a>
                                <a href="/consultation" class="list-group-item list-group-item-action">
                                    <i class="fas fa-comments me-2"></i>在线咨询客服
                                </a>
                                <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="alert('请联系技术支持热线或发送邮件反馈问题')">
                                    <i class="fas fa-bug me-2"></i>提交问题反馈
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary" style="position: fixed; bottom: 20px; right: 20px; display: none; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 平滑滚动到锚点
    $('a[href^="#"]').on('click', function(event) {
        var href = this.getAttribute('href');
        // 检查href是否为空或只是#
        if (href && href !== '#' && href.length > 1) {
            var target = $(href);
            if (target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
            }
        }
    });

    // 修复空链接问题
    $('a[href="#"]').on('click', function(event) {
        event.preventDefault();
    });

    // 返回顶部按钮功能
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('#backToTop').fadeIn();
        } else {
            $('#backToTop').fadeOut();
        }
    });

    $('#backToTop').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    // 为快速导航按钮添加活跃状态
    $(window).scroll(function() {
        var scrollPos = $(document).scrollTop();
        $('.btn-outline-primary, .btn-outline-info, .btn-outline-success, .btn-outline-warning, .btn-outline-danger, .btn-outline-secondary, .btn-outline-dark').each(function() {
            var currLink = $(this);
            var refElement = $(currLink.attr('href'));
            if (refElement.length && refElement.position().top <= scrollPos + 150 && refElement.position().top + refElement.height() > scrollPos + 150) {
                currLink.addClass('active');
            } else {
                currLink.removeClass('active');
            }
        });
    });
});
</script>
{% endblock %}
