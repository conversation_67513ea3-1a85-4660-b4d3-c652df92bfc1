<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <style nonce="{{ csp_nonce }}">
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }

        .container {
            max-width: 600px;
        }

        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }

        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            text-align: center;
            padding: 20px;
        }

        .rating-stars {
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .rating-star {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
        }

        .rating-star.active {
            color: #ffc107;
        }

        .rating-star:hover {
            color: #ffc107;
        }

        .anonymous-name {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px 12px;
            font-weight: 500;
            color: #495057;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
        }

        .photo-preview {
            max-width: 100px;
            max-height: 100px;
            margin: 5px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- 返回按钮 -->
                <div class="mb-3">
                    <a href="{{ url_for('daily_management.companion_entry', school_id=school.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回首页
                    </a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">{{ school.name }}</h4>
                        <small>添加陪餐记录 - {{ log.log_date }}</small>
                    </div>

                    <div class="card-body">
                        <form method="post" action="{{ url_for('daily_management.public_submit_companion', log_id=log.id) }}" enctype="multipart/form-data" novalidate novalidate>
                            {{ form.csrf_token }}

                            <!-- 基本信息 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="companion_name" class="form-label">陪餐人姓名</label>
                                    <div class="anonymous-name" id="companion_name_display">匿名用户_{{ range(1000, 9999) | random }}</div>
                                    <input type="hidden" id="companion_name" name="companion_name" value="匿名用户_{{ range(1000, 9999) | random }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="companion_role" class="form-label">陪餐人角色 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="companion_role" name="companion_role" required>
                                        <option value="">请选择您的角色</option>
                                        <option value="校长">校长</option>
                                        <option value="副校长">副校长</option>
                                        <option value="主任">主任</option>
                                        <option value="教师">教师</option>
                                        <option value="家长">家长</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 餐次信息 -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="meal_type" class="form-label">餐次 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="meal_type" name="meal_type" required>
                                        <option value="">请选择餐次</option>
                                        <option value="breakfast">早餐</option>
                                        <option value="lunch">午餐</option>
                                        <option value="dinner">晚餐</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 日期时间信息 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="dining_date" class="form-label">陪餐日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="dining_date" name="dining_date" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="dining_time" class="form-label">陪餐时间 <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="dining_time" name="dining_time" value="12:00" required>
                                </div>
                            </div>

                            <!-- 评分部分 -->
                            <div class="mb-4">
                                <h6 class="mb-3"><i class="fas fa-star text-warning me-2"></i>评价评分</h6>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">口味评分</label>
                                        <div class="rating-stars" id="taste-rating">
                                            <span class="rating-star" data-value="1">★</span>
                                            <span class="rating-star" data-value="2">★</span>
                                            <span class="rating-star" data-value="3">★</span>
                                            <span class="rating-star" data-value="4">★</span>
                                            <span class="rating-star" data-value="5">★</span>
                                        </div>
                                        <input type="hidden" name="taste_rating" id="taste_rating_input" value="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">卫生评分</label>
                                        <div class="rating-stars" id="hygiene-rating">
                                            <span class="rating-star" data-value="1">★</span>
                                            <span class="rating-star" data-value="2">★</span>
                                            <span class="rating-star" data-value="3">★</span>
                                            <span class="rating-star" data-value="4">★</span>
                                            <span class="rating-star" data-value="5">★</span>
                                        </div>
                                        <input type="hidden" name="hygiene_rating" id="hygiene_rating_input" value="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">服务评分</label>
                                        <div class="rating-stars" id="service-rating">
                                            <span class="rating-star" data-value="1">★</span>
                                            <span class="rating-star" data-value="2">★</span>
                                            <span class="rating-star" data-value="3">★</span>
                                            <span class="rating-star" data-value="4">★</span>
                                            <span class="rating-star" data-value="5">★</span>
                                        </div>
                                        <input type="hidden" name="service_rating" id="service_rating_input" value="0">
                                    </div>
                                </div>
                            </div>

                            <!-- 意见建议 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="comments" class="form-label">评价意见</label>
                                    <textarea class="form-control" id="comments" name="comments" rows="3" placeholder="请分享您对食堂的整体评价..."></textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="suggestions" class="form-label">改进建议</label>
                                    <textarea class="form-control" id="suggestions" name="suggestions" rows="3" placeholder="请提出您的改进建议..."></textarea>
                                </div>
                            </div>

                            <!-- 照片上传 -->
                            <div class="mb-3">
                                <label for="photos" class="form-label">照片上传</label>
                                <input class="form-control" type="file" id="photos" name="photos" multiple accept="image/*">
                                <div class="form-text">可以选择多张照片上传，支持jpg、jpeg、png格式</div>
                                <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>提交陪餐记录
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <script nonce="{{ csp_nonce }}">
        // 简单的星级评分
        function setupRating(containerId, inputId) {
            const container = document.getElementById(containerId);
            const input = document.getElementById(inputId);
            const stars = container.querySelectorAll('.rating-star');

            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    input.value = value;
                    updateStars(stars, value);
                });

                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    updateStars(stars, value);
                });

                container.addEventListener('mouseleave', function() {
                    const value = parseInt(input.value);
                    updateStars(stars, value);
                });
            });
        }

        function updateStars(stars, value) {
            stars.forEach(s => {
                if (parseInt(s.getAttribute('data-value')) <= value) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        }

        // 照片预览
        document.getElementById('photos').addEventListener('change', function(e) {
            const previewsDiv = document.getElementById('photo-previews');
            previewsDiv.innerHTML = '';

            for (const file of this.files) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.className = 'photo-preview';
                    previewsDiv.appendChild(img);
                }
                reader.readAsDataURL(file);
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupRating('taste-rating', 'taste_rating_input');
            setupRating('hygiene-rating', 'hygiene_rating_input');
            setupRating('service-rating', 'service_rating_input');
        });
    </script>
</body>
</html>
