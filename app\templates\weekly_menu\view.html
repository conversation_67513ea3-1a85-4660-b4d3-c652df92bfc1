{% extends 'base.html' %}

{% block title %}周菜单详情 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ weekly_menu.area.name }}周菜单详情</h2>
      <p class="text-muted">{{ weekly_menu.week_start|format_datetime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end|format_datetime('%Y-%m-%d') }}</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary me-2">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
      <a href="{{ url_for('weekly_menu_v2.view', id=weekly_menu.id) }}" class="btn btn-info me-2">
        <i class="fas fa-rocket"></i> 使用新版查看
      </a>
      {% if weekly_menu.status == '计划中' %}
      <a href="{{ url_for('weekly_menu.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start|format_datetime('%Y-%m-%d')) }}" class="btn btn-primary">
        <i class="fas fa-edit"></i> 编辑菜单
      </a>
      <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#publishModal">
        <i class="fas fa-check-circle"></i> 发布菜单
      </button>
      {% endif %}
      <a href="{{ url_for('weekly_menu.print_menu', id=weekly_menu.id, t=now()|int) }}" class="btn btn-info" target="_blank">
        <i class="fas fa-print"></i> 打印菜单
      </a>
      <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
        <i class="fas fa-trash"></i> 删除菜单
      </button>
    </div>
  </div>

  <!-- 菜单基本信息 -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">基本信息</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <p><strong>学校：</strong> {{ weekly_menu.area.name }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>周开始日期：</strong> {{ weekly_menu.week_start|format_datetime('%Y-%m-%d') }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>周结束日期：</strong> {{ weekly_menu.week_end|format_datetime('%Y-%m-%d') }}</p>
        </div>
        <div class="col-md-3">
          <p>
            <strong>状态：</strong>
            {% if weekly_menu.status == '计划中' %}
            <span class="badge badge-warning">计划中</span>
            {% elif weekly_menu.status == '已发布' %}
            <span class="badge badge-success">已发布</span>
            {% else %}
            <span class="badge badge-secondary">{{ weekly_menu.status }}</span>
            {% endif %}
          </p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <p><strong>创建人：</strong> {{ weekly_menu.creator.real_name or weekly_menu.creator.username }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>创建时间：</strong> {{ weekly_menu.created_at|format_datetime }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>最后更新：</strong> {{ weekly_menu.updated_at|format_datetime }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 周菜单表格 -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">周菜单安排</h5>
    </div>
    <div class="card-body">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="w-15">日期</th>
            <th class="w-30">早餐</th>
            <th class="w-30">午餐</th>
            <th class="w-30">晚餐</th>
          </tr>
        </thead>
        <tbody>
          {% for date_str, day_data in week_data.items() %}
          <tr>
            <td>
              <div class="fw-bold">{{ day_data.weekday }}</div>
              <div>{{ date_str }}</div>
            </td>
            {% for meal_type in ['早餐', '午餐', '晚餐'] %}
            <td>
              {% if day_data.meals[meal_type] %}
                {% for recipe in day_data.meals[meal_type] %}
                <div class="badge badge-info mb-1 d-inline-block">{{ recipe.name }}</div>
                {% endfor %}
              {% else %}
                <span class="text-muted">未安排</span>
              {% endif %}
            </td>
            {% endfor %}
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- 发布确认对话框 -->
<div class="modal fade" id="publishModal" tabindex="-1" role="dialog" aria-labelledby="publishModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="publishModalLabel">确认发布</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>您确定要发布这个周菜单计划吗？发布后将不能再进行编辑。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <form action="{{ url_for('weekly_menu.publish', id=weekly_menu.id) }}" method="post" novalidate novalidate>
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-success">确认发布</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p class="text-danger"><strong>警告：</strong>此操作不可逆！</p>
        <p>您确定要删除这个周菜单计划吗？删除后将无法恢复。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <form action="{{ url_for('weekly_menu.delete_menu', id=weekly_menu.id) }}" method="post" novalidate novalidate>
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-danger">确认删除</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .badge-info {
    font-size: 0.9em;
    padding: 5px 8px;
    margin-right: 5px;
  }
</style>
{% endblock %}
