/* 左右布局修复样式 */

/* 确保基本布局正常工作 */
.app-wrapper {
    display: flex !important;
    min-height: calc(100vh - 60px) !important;
}

.sidebar {
    width: 200px !important;
    position: fixed !important;
    top: 60px !important;
    left: 0 !important;
    height: calc(100vh - 60px) !important;
    background: #f8f9fa !important;
    border-right: 1px solid #dee2e6 !important;
    overflow-y: auto !important;
    z-index: 1020 !important;
}

.main-content {
    margin-left: 200px !important;
    margin-top: 60px !important;
    flex: 1 !important;
    padding: 20px !important;
    min-height: calc(100vh - 60px - 80px) !important;
}

.top-toolbar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 60px !important;
    z-index: 1030 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 20px !important;
}

.footer {
    margin-left: 200px !important;
    margin-top: auto !important;
    padding: 15px 20px !important;
    text-align: center !important;
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }
    
    .sidebar.show {
        transform: translateX(0) !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .footer {
        margin-left: 0 !important;
    }
    
    .sidebar-toggle {
        display: block !important;
    }
}

@media (min-width: 769px) {
    .sidebar-toggle {
        display: none !important;
    }
}

/* 确保内容不被遮挡 */
body {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

.container, .container-fluid {
    max-width: 100% !important;
}

/* 侧边栏导航样式 */
.sidebar-nav {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.sidebar-nav .nav-item {
    border-bottom: 1px solid #e9ecef !important;
}

.sidebar-nav .nav-link {
    display: block !important;
    padding: 12px 20px !important;
    color: #495057 !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.sidebar-nav .nav-link:hover {
    background: #e9ecef !important;
    color: #212529 !important;
    text-decoration: none !important;
}

.sidebar-nav .nav-link.active {
    background: var(--bs-primary, #0d6efd) !important;
    color: white !important;
}

.sidebar-nav .nav-link i {
    width: 20px !important;
    text-align: center !important;
    margin-right: 10px !important;
}

/* 工具栏按钮样式 */
.toolbar-btn {
    background: none !important;
    border: none !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.toolbar-btn:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
    text-decoration: none !important;
}

/* 品牌文字样式 */
.brand-text {
    color: white !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.brand-text:hover {
    color: rgba(255,255,255,0.9) !important;
    text-decoration: none !important;
}

/* 确保下拉菜单正常显示 */
.dropdown-menu {
    z-index: 1040 !important;
}

/* 移动端遮罩层 */
.sidebar-overlay {
    position: fixed !important;
    top: 60px !important;
    left: 0 !important;
    width: 100% !important;
    height: calc(100vh - 60px) !important;
    background: rgba(0,0,0,0.5) !important;
    z-index: 1019 !important;
    display: none !important;
}

.sidebar-overlay.show {
    display: block !important;
}
