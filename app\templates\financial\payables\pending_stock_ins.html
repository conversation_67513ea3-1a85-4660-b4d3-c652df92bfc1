{% extends "financial/base.html" %}

{% block title %}待生成应付账款的入库单{% endblock %}

{% block financial_content %}
<!-- 用友风格页面头部 -->
<div class="uf-page-header">
    <div class="uf-page-title">
        <i class="fas fa-file-invoice-dollar uf-icon"></i>
        <span>待生成应付账款的入库单</span>
    </div>
    <div class="uf-page-actions">
        <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-secondary">
            <i class="fas fa-arrow-left uf-icon"></i> 返回应付账款列表
        </a>
    </div>
</div>

<!-- 用友风格主要内容区域 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-list uf-icon"></i> 待处理入库单列表
        <div class="uf-card-tools">
            <span class="uf-badge uf-badge-info">{{ stock_ins|length if stock_ins else 0 }} 条记录</span>
        </div>
    </div>
    <div class="uf-card-body">
        {% if stock_ins %}
        <!-- 用友风格提示信息 -->
        <div class="uf-alert uf-alert-info">
            <i class="fas fa-info-circle uf-icon"></i>
            <strong>操作提示：</strong>以下入库单已完成财务确认，可以生成应付账款。请选择需要生成应付账款的入库单。
        </div>

        <!-- 用友财务专业工具栏 -->
        <div class="uf-financial-toolbar">
            <div class="uf-financial-toolbar-left">
                <span class="uf-financial-toolbar-title">待处理入库单</span>
                <button class="uf-financial-btn generate" onclick="batchGenerate()">
                    <i class="fas fa-layer-group uf-financial-icon"></i>批量生成应付账款
                </button>
                <button class="uf-financial-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt uf-financial-icon"></i>刷新数据
                </button>
            </div>
            <div class="uf-financial-toolbar-right">
                <span style="color: #666; font-size: 12px;">合计金额：</span>
                <span class="uf-financial-amount">¥{{ "%.2f"|format(stock_ins|sum(attribute='total_cost')) }}</span>
            </div>
        </div>

        <!-- 用友财务专业数据表格 -->
        <div class="uf-table-container">
            <table class="uf-financial-table">
                <thead>
                    <tr>
                        <th class="col-voucher">入库单号</th>
                        <th class="col-subject-name">供应商</th>
                        <th class="col-date">入库日期</th>
                        <th class="col-amount">总金额</th>
                        <th class="col-voucher">采购订单</th>
                        <th class="col-date">财务确认时间</th>
                        <th class="col-actions">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stock_in in stock_ins %}
                    <tr data-id="{{ stock_in.id }}">
                        <td class="col-voucher">
                            <span class="uf-voucher-number">{{ stock_in.stock_in_number }}</span>
                        </td>
                        <td class="col-subject-name">
                            <span class="uf-subject-name">{{ stock_in.supplier.name if stock_in.supplier else '未知供应商' }}</span>
                        </td>
                        <td class="col-date">
                            <span class="uf-financial-date">{{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else '未知' }}</span>
                        </td>
                        <td class="col-amount">
                            <span class="uf-financial-amount">¥{{ "%.2f"|format(stock_in.total_cost) }}</span>
                        </td>
                        <td class="col-voucher">
                            {% if stock_in.purchase_order %}
                            <span class="uf-voucher-number">{{ stock_in.purchase_order.order_number }}</span>
                            {% else %}
                            <span style="color: #999;">-</span>
                            {% endif %}
                        </td>
                        <td class="col-date">
                            <span class="uf-financial-date">{{ stock_in.financial_confirmed_at.strftime('%Y-%m-%d %H:%M') if stock_in.financial_confirmed_at else '未知' }}</span>
                        </td>
                        <td class="col-actions">
                            <button class="uf-financial-btn generate"
                                    onclick="generatePayable({{ stock_in.id }})"
                                    title="生成应付账款">
                                <i class="fas fa-plus uf-financial-icon"></i>生成应付账款
                            </button>
                            <a href="{{ url_for('stock_in.view', id=stock_in.id) }}"
                               class="uf-financial-btn"
                               title="查看入库单详情"
                               target="_blank">
                                <i class="fas fa-eye uf-financial-icon"></i>查看
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr style="background: #f8f9fa; font-weight: 600;">
                        <td colspan="3" style="text-align: right; font-weight: 600;">合计金额：</td>
                        <td class="col-amount">
                            <span class="uf-financial-amount" style="color: #d32f2f; font-size: 13px; font-weight: 600;">¥{{ "%.2f"|format(stock_ins|sum(attribute='total_cost')) }}</span>
                        </td>
                        <td colspan="3"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <!-- 用友风格空状态 -->
        <div class="uf-empty-state">
            <div class="uf-empty-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="uf-empty-title">所有入库单已处理完成</div>
            <div class="uf-empty-description">
                所有已财务确认的入库单都已生成应付账款，暂无待处理的入库单。
            </div>
            <div class="uf-empty-actions">
                <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-primary">
                    <i class="fas fa-list uf-icon"></i> 查看应付账款列表
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block financial_css %}
<style>
/* 用友风格页面头部 */
.uf-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--uf-border);
    margin-bottom: 16px;
}

.uf-page-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--uf-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-page-actions {
    display: flex;
    gap: 8px;
}

/* 用友风格工具栏 */
.uf-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--uf-border);
    margin-bottom: 16px;
}

.uf-toolbar-left {
    display: flex;
    gap: 8px;
}

.uf-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

/* 用友风格表格容器 */
.uf-table-container {
    border: 1px solid var(--uf-border);
    border-radius: 2px;
    overflow: hidden;
}

/* 用友风格表格行 */
.uf-table-row:hover {
    background-color: var(--uf-row-hover);
}

.uf-table-footer {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 用友风格供应商信息 */
.uf-supplier-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.uf-icon-sm {
    font-size: 10px;
    color: #666;
}

/* 用友风格日期时间 */
.uf-date, .uf-datetime {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #666;
}

/* 用友风格按钮组 */
.uf-btn-group {
    display: flex;
    gap: 4px;
    justify-content: center;
}

/* 用友风格空状态 */
.uf-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.uf-empty-icon {
    font-size: 48px;
    color: var(--uf-success);
    margin-bottom: 16px;
}

.uf-empty-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--uf-text);
    margin-bottom: 8px;
}

.uf-empty-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 24px;
    line-height: 1.5;
}

.uf-empty-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

/* 用友风格金额显示 */
.uf-amount {
    font-family: 'Arial', sans-serif;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-amount-total {
    font-size: 13px;
    color: var(--uf-danger);
}

/* 用友风格代码显示 */
.uf-code-secondary {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@d-flex (max-width: 768px) {
    .uf-page-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .uf-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .uf-toolbar-right {
        align-self: flex-end;
    }

    .uf-btn-group {
        flex-direction: column;
        gap: 4px;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 用友风格应付账款生成功能
function generatePayable(stockInId) {
    console.log('=== 开始生成应付账款 ===');
    console.log('入库单ID:', stockInId);

    // 用友风格确认对话框
    if (confirm('确定要为此入库单生成应付账款吗？\n\n生成后将自动创建对应的应付账款记录。')) {
        console.log('用户确认生成');

        // 显示加载状态
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin uf-financial-icon"></i>生成中...';
        button.disabled = true;

        console.log('开始发送请求...');

        fetch('{{ url_for("financial.generate_payable_from_stock_in") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                stock_in_id: stockInId
            })
        })
        .then(response => {
            console.log('收到响应:', response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log('响应数据:', data);
            if (data.success) {
                console.log('生成成功');
                // 用友风格成功提示
                showUFMessage('应付账款生成成功！', 'success');
                // 移除该行
                const row = document.querySelector(`[data-id="${stockInId}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                        updateSummary();
                    }, 300);
                }
            } else {
                console.log('生成失败:', data.message);
                showUFMessage('生成失败：' + data.message, 'error');
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('请求错误:', error);
            showUFMessage('操作失败，请重试', 'error');
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// 用友风格批量生成功能
function batchGenerate() {
    const stockInIds = [];
    {% for stock_in in stock_ins %}
    stockInIds.push({{ stock_in.id }});
    {% endfor %}

    if (stockInIds.length === 0) {
        showUFMessage('没有可处理的入库单', 'warning');
        return;
    }

    if (confirm(`确定要为所有 ${stockInIds.length} 个入库单批量生成应付账款吗？\n\n此操作将逐个处理每个入库单，请耐心等待。`)) {
        // 显示进度
        const progressModal = showProgressModal('批量生成应付账款', stockInIds.length);

        let completed = 0;
        let failed = 0;

        function generateNext(index) {
            if (index >= stockInIds.length) {
                hideProgressModal();
                const message = `批量生成完成！\n成功：${completed} 个\n失败：${failed} 个`;
                showUFMessage(message, failed === 0 ? 'success' : 'warning');
                if (completed > 0) {
                    setTimeout(() => location.reload(), 2000);
                }
                return;
            }

            // 更新进度
            updateProgress(index + 1, stockInIds.length, `正在处理第 ${index + 1} 个入库单...`);

            fetch('{{ url_for("financial.generate_payable_from_stock_in") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    stock_in_id: stockInIds[index]
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    completed++;
                } else {
                    failed++;
                    console.error('Failed for stock_in_id:', stockInIds[index], data.message);
                }
                setTimeout(() => generateNext(index + 1), 500); // 延迟处理，避免过快
            })
            .catch(error => {
                failed++;
                console.error('Error for stock_in_id:', stockInIds[index], error);
                setTimeout(() => generateNext(index + 1), 500);
            });
        }

        generateNext(0);
    }
}

// 刷新数据
function refreshData() {
    showUFMessage('正在刷新数据...', 'info');
    location.reload();
}

// 更新汇总信息
function updateSummary() {
    const rows = document.querySelectorAll('.uf-table-row');
    const badge = document.querySelector('.uf-badge');
    if (badge) {
        badge.textContent = `${rows.length} 条记录`;
    }

    // 如果没有记录了，显示空状态
    if (rows.length === 0) {
        setTimeout(() => location.reload(), 1000);
    }
}

// 用友风格消息提示
function showUFMessage(message, type = 'info') {
    const alertClass = {
        'success': 'uf-alert-success',
        'error': 'uf-alert-danger',
        'warning': 'uf-alert-warning',
        'info': 'uf-alert-info'
    }[type] || 'uf-alert-info';

    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';

    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `uf-alert ${alertClass}`;
    messageEl.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; animation: slideInRight 0.3s ease;';
    messageEl.innerHTML = `
        <i class="${icon} uf-icon"></i>
        <span>${message}</span>
        <button type="button" class="uf-alert-close" onclick="this.parentElement.remove()">×</button>
    `;

    document.body.appendChild(messageEl);

    // 自动移除
    setTimeout(() => {
        if (messageEl.parentElement) {
            messageEl.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => messageEl.remove(), 300);
        }
    }, 3000);
}

// 进度模态框
function showProgressModal(title, total) {
    const modal = document.createElement('div');
    modal.className = 'uf-modal-overlay';
    modal.innerHTML = `
        <div class="uf-modal uf-modal-sm">
            <div class="uf-modal-header">
                <h5>${title}</h5>
            </div>
            <div class="uf-modal-body">
                <div class="uf-progress">
                    <div class="uf-progress-bar" style="width: 0%"></div>
                </div>
                <div class="uf-progress-text">准备开始...</div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    return modal;
}

function updateProgress(current, total, text) {
    const progressBar = document.querySelector('.uf-progress-bar');
    const progressText = document.querySelector('.uf-progress-text');
    if (progressBar && progressText) {
        const percent = Math.round((current / total) * 100);
        progressBar.style.width = `${percent}%`;
        progressText.textContent = `${text} (${current}/${total})`;
    }
}

function hideProgressModal() {
    const modal = document.querySelector('.uf-modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('用友风格待生成应付账款页面初始化完成');

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        .uf-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        .uf-modal {
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            min-width: 400px;
        }
        .uf-modal-sm {
            min-width: 300px;
        }
        .uf-modal-header {
            padding: 16px;
            border-bottom: 1px solid var(--uf-border);
            font-weight: 600;
        }
        .uf-modal-body {
            padding: 20px;
        }
        .uf-progress {
            background: #f0f0f0;
            border-radius: 2px;
            height: 8px;
            margin-bottom: 12px;
            overflow: hidden;
        }
        .uf-progress-bar {
            background: var(--uf-primary);
            height: 100%;
            transition: width 0.3s ease;
        }
        .uf-progress-text {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .uf-alert-close {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            margin-left: 12px;
            color: inherit;
            opacity: 0.7;
        }
        .uf-alert-close:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
