{% extends 'base.html' %}

{% block title %}编辑用户权限 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
        <p class="text-muted">为用户 <strong>{{ user.username }}</strong> 分配角色和权限</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回用户详情
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">用户信息</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label>用户名</label>
                    <p class="form-control-static">{{ user.username }}</p>
                </div>
                <div class="mb-3">
                    <label>真实姓名</label>
                    <p class="form-control-static">{{ user.real_name or '未设置' }}</p>
                </div>
                <div class="mb-3">
                    <label>电子邮箱</label>
                    <p class="form-control-static">{{ user.email or '未设置' }}</p>
                </div>
                <div class="mb-3">
                    <label>所属区域</label>
                    <p class="form-control-static">
                        {% if user.area %}
                        {{ user.area.get_level_name() }} - {{ user.area.name }}
                        {% else %}
                        未设置
                        {% endif %}
                    </p>
                </div>
                <div class="mb-3">
                    <label>账号状态</label>
                    <p class="form-control-static">
                        {% if user.status == 1 %}
                        <span class="badge badge-success">启用</span>
                        {% else %}
                        <span class="badge badge-danger">禁用</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">角色分配</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate novalidate><div class="mb-3">
                        <label>选择角色</label>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 为用户分配角色将决定用户可以执行的操作。一个用户可以拥有多个角色，权限将合并计算。
                        </div>
                        
                        <div class="role-list">
                            {% for role in roles %}
                            <div class="custom-control custom-checkbox mb-2">
                                <input type="checkbox" class="form-check-input" id="role_{{ role.id }}" name="roles" value="{{ role.id }}" 
                                       {% if role.id in current_role_ids %}checked{% endif %}>
                                <label class="form-check-label" for="role_{{ role.id }}">
                                    <strong>{{ role.name }}</strong>
                                    {% if role.description %}
                                    <small class="text-muted ms-2">{{ role.description }}</small>
                                    {% endif %}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="mb-3 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存权限设置
                        </button>
                        <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">权限说明</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 权限是通过角色分配的。如果您需要自定义权限，请先创建一个新的角色，然后将该角色分配给用户。
                </div>
                
                <p>权限控制规则：</p>
                <ul>
                    <li>用户的实际权限是其所有角色权限的合集</li>
                    <li>系统管理员角色拥有系统中的所有权限</li>
                    <li>用户只能访问自己所在区域及其下级区域的数据</li>
                    <li>上级管理员可以管理下级区域的用户权限</li>
                </ul>
                
                <div class="text-end">
                    <a href="{{ url_for('system.permission_help') }}" target="_blank" class="btn btn-sm btn-info">
                        <i class="fas fa-question-circle"></i> 查看权限配置帮助
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 如果用户是系统管理员，禁用系统管理员角色的复选框
    {% if not current_user.is_admin() %}
    $('input[type="checkbox"]').each(function() {
        var label = $(this).next('label').text().trim();
        if (label.startsWith('系统管理员')) {
            $(this).prop('disabled', true);
            $(this).parent().append('<small class="text-danger ms-2">（需要系统管理员权限才能分配）</small>');
        }
    });
    {% endif %}
});
</script>
{% endblock %}
