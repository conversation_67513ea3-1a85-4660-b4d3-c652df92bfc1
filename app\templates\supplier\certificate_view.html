{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplier_certificate.edit', id=certificate.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('supplier_certificate.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">供应商</th>
                                    <td>{{ certificate.supplier.name }}</td>
                                </tr>
                                <tr>
                                    <th>证书类型</th>
                                    <td>{{ certificate.certificate_type if certificate.certificate_type is defined else certificate.name }}</td>
                                </tr>
                                <tr>
                                    <th>证书编号</th>
                                    <td>{{ certificate.certificate_number if certificate.certificate_number is defined else certificate.number }}</td>
                                </tr>
                                <tr>
                                    <th>发证机构</th>
                                    <td>{{ certificate.issuing_authority }}</td>
                                </tr>
                                <tr>
                                    <th>发证日期</th>
                                    <td>{{  certificate.issue_date|format_datetime('%Y-%m-%d')  }}</td>
                                </tr>
                                <tr>
                                    <th>到期日期</th>
                                    <td>{{  certificate.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if certificate.status == '有效' %}
                                        <span class="badge badge-success">有效</span>
                                        {% elif certificate.status == '即将过期' %}
                                        <span class="badge badge-warning">即将过期</span>
                                        {% else %}
                                        <span class="badge badge-danger">已过期</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">证书图片</h5>
                                </div>
                                <div class="card-body text-center">
                                    {% if certificate.certificate_image %}
                                        <img src="{{ url_for('static', filename=certificate.certificate_image|fix_path) }}"
                                             alt="证书图片"
                                             class="img-fluid"
                                             style="max-height: 400px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;"
                                             onclick="showImageModal(this.src)">
                                        <div class="mt-2">
                                            <small class="text-muted">点击图片查看大图</small>
                                        </div>
                                    {% else %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">暂无证书图片</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">证书操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group">
                                        <a href="{{ url_for('supplier_certificate.edit', id=certificate.id) }}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> 编辑证书
                                        </a>
                                        <button type="button" class="btn btn-danger delete-btn" data-id="{{ certificate.id }}">
                                            <i class="fas fa-trash"></i> 删除证书
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个证书吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">证书图片</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="证书图片" class="img-fluid" style="max-width: 100%; max-height: 80vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a id="downloadImage" href="" download class="btn btn-primary">
                    <i class="fas fa-download"></i> 下载图片
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier_certificate.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.href = '{{ url_for("supplier_certificate.index") }}';
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });
    });

    // 图片查看功能
    function showImageModal(imageSrc) {
        $('#modalImage').attr('src', imageSrc);
        $('#downloadImage').attr('href', imageSrc);
        $('#imageModal').modal('show');
    }
</script>
{% endblock %}
