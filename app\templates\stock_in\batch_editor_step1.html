{% extends 'base.html' %}

{% block title %}批次编辑器 (步骤1) - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .batch-editor-container {
    margin-top: 20px;
  }
  .batch-card {
    margin-bottom: 15px;
    border-start: 4px solid #007bff;
  }
  .batch-header {
    background-color: #f8f9fa;
    padding: 10px;
    cursor: pointer;
  }
  .batch-body {
    padding: 15px;
  }
  .supplier-select {
    max-width: 300px;
  }
  .action-buttons {
    margin-top: 20px;
  }
  .batch-table th, .batch-table td {
    vertical-align: middle;
  }
  .batch-group {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
  .batch-group-header {
    background-color: #e9ecef;
    padding: 10px 15px;
    font-weight: bold;
  }
  .batch-items {
    padding: 15px;
  }
  .row g-3 {
    margin-bottom: 15px;
  }
  
  /* 新增样式 */
  .highlight-box {
    background-color: #f8f9fa;
    border-start: 4px solid #dc3545;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .batch-table th.bg-light {
    background-color: #f8f9fa !important;
    font-size: 1.1rem;
    font-weight: bold;
  }
  
  .batch-table input[type="number"] {
    transition: all 0.3s;
  }
  
  .batch-table input[type="number"]:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background-color: #fff9f9;
  }
  
  .batch-row:hover {
    background-color: #f8f9fa;
  }
  
  .batch-row td {
    padding: 12px 8px;
  }
  
  .total-price {
    font-weight: bold;
    color: #dc3545;
    font-size: 1.1rem;
  }
  
  .step-indicator {
    display: flex;
    margin-bottom: 20px;
  }
  
  .step {
    flex: 1;
    text-align: center;
    padding: 10px;
    position: relative;
  }
  
  .step.active {
    font-weight: bold;
    color: #4e73df;
  }
  
  .step.completed {
    color: #1cc88a;
  }
  
  .step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 100%;
    height: 2px;
    background-color: #e3e6f0;
    z-index: -1;
  }
  
  .step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    margin-bottom: 5px;
  }
  
  .step.active .step-number {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
  }
  
  .step.completed .step-number {
    background-color: #1cc88a;
    color: white;
    border-color: #1cc88a;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 fw-bold text-primary">批次编辑器 - 步骤1：基本信息</h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">批次操作:</div>
              <a class="dropdown-item" href="#" id="selectAllBatches"><i class="fas fa-check-square fa-sm fa-fw me-2 text-gray-400"></i>全选</a>
              <a class="dropdown-item" href="#" id="deselectAllBatches"><i class="far fa-square fa-sm fa-fw me-2 text-gray-400"></i>取消全选</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="#" id="groupByIngredient"><i class="fas fa-object-group fa-sm fa-fw me-2 text-gray-400"></i>按食材分组</a>
              <a class="dropdown-item" href="#" id="groupByBatch"><i class="fas fa-layer-group fa-sm fa-fw me-2 text-gray-400"></i>按批次分组</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 步骤指示器 -->
          <div class="step-indicator">
            <div class="step active">
              <div class="step-number">1</div>
              <div>基本信息</div>
            </div>
            <div class="step">
              <div class="step-number">2</div>
              <div>日期与单据</div>
            </div>
          </div>
          
          <div class="highlight-box">
            <h5><i class="fas fa-info-circle text-danger"></i> 步骤1：基本信息绑定</h5>
            <p>在此步骤中，请完成以下操作：</p>
            <ol>
              <li>勾选需要批量编辑的食材</li>
              <li>为每个食材指定<strong>供应商</strong>和<strong>存储位置</strong></li>
              <li>填写准确的<strong>数量</strong>和<strong>单价</strong></li>
              <li>完成后点击<strong>下一步</strong>按钮，进入日期与单据绑定</li>
            </ol>
            <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle"></i> 注意：所有字段都是必填的，请确保填写完整。</p>
          </div>

          <!-- 批量操作表单 -->
          <form id="batchEditForm" method="post" action="{{ url_for('stock_in.save_batch_edit_step1', stock_in_id=stock_in.id) }}" novalidate novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <!-- 批量操作工具栏 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="bulkSupplier"><strong>批量设置供应商:</strong></label>
                  <div class="input-group">
                    <select class="form-control" id="bulkSupplier">
                      <option value="">-- 请选择供应商 --</option>
                      {% for supplier in suppliers %}
                      <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                      {% endfor %}
                    </select>
                    <div >
                      <button class="btn btn-primary" type="button" id="applyBulkSupplier">应用</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="bulkStorageLocation"><strong>批量设置存储位置:</strong></label>
                  <div class="input-group">
                    <select class="form-control" id="bulkStorageLocation">
                      <option value="">-- 请选择存储位置 --</option>
                      {% for location in storage_locations %}
                      <option value="{{ location.id }}">{{ location.name }}</option>
                      {% endfor %}
                    </select>
                    <div >
                      <button class="btn btn-primary" type="button" id="applyBulkStorageLocation">应用</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 批次列表 -->
            <div class="table-responsive">
              <table class="table table-bordered batch-table" id="batchTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th width="5%">
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="selectAll">
                        <label class="form-check-label" for="selectAll"></label>
                      </div>
                    </th>
                    <th width="20%">食材</th>
                    <th width="25%">供应商</th>
                    <th width="20%">存储位置</th>
                    <th width="15%" class="bg-light text-danger">数量</th>
                    <th width="15%" class="bg-light text-danger">单价(元)</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in stock_in_items %}
                  <tr class="batch-row" data-ingredient-id="{{ item.ingredient_id }}" data-batch-number="{{ item.batch_number }}">
                    <td>
                      <div class="form-check">
                        <input type="checkbox" class="custom-control-input batch-checkbox" id="batch{{ loop.index }}" name="selected_items[]" value="{{ item.id }}">
                        <label class="form-check-label" for="batch{{ loop.index }}"></label>
                      </div>
                    </td>
                    <td>
                      {{ item.ingredient.name }}
                      <input type="hidden" name="batch_number_{{ item.id }}" value="{{ item.batch_number }}">
                      <input type="hidden" name="unit_{{ item.id }}" value="{{ item.unit }}">
                      <small class="text-muted d-block">{{ item.batch_number }}</small>
                    </td>
                    <td>
                      <select class="form-control supplier-select" name="supplier_id_{{ item.id }}" required>
                        <option value="">-- 请选择供应商 --</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if item.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                      </select>
                    </td>
                    <td>
                      <select class="form-control" name="storage_location_id_{{ item.id }}" required>
                        <option value="">-- 请选择 --</option>
                        {% for location in storage_locations %}
                        <option value="{{ location.id }}" {% if item.storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                        {% endfor %}
                      </select>
                    </td>
                    <td>
                      <input type="number" class="form-control form-control-lg fw-bold text-danger"
                             name="quantity_{{ item.id }}" value="{{ item.quantity }}" step="0.01" min="0"
                             style="font-size: 1.2rem;" required>
                      <small class="text-muted">{{ item.unit }}</small>
                    </td>
                    <td>
                      <input type="number" class="form-control form-control-lg fw-bold text-danger"
                             name="unit_price_{{ item.id }}" value="{{ item.unit_price }}" step="0.01" min="0"
                             style="font-size: 1.2rem;" required>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons text-center">
              <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-times"></i> 取消
              </a>
              <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-right"></i> 下一步：日期与单据
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 全选/取消全选
    $("#selectAll").change(function() {
      $(".batch-checkbox").prop('checked', $(this).prop('checked'));
    });
    
    // 批量设置供应商
    $("#applyBulkSupplier").click(function() {
      var supplierId = $("#bulkSupplier").val();
      if (!supplierId) {
        alert("请先选择供应商");
        return;
      }
      
      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("select[name='supplier_id_" + itemId + "']").val(supplierId);
      });
    });
    
    // 批量设置存储位置
    $("#applyBulkStorageLocation").click(function() {
      var locationId = $("#bulkStorageLocation").val();
      if (!locationId) {
        alert("请先选择存储位置");
        return;
      }
      
      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("select[name='storage_location_id_" + itemId + "']").val(locationId);
      });
    });
    
    // 下拉菜单操作
    $("#selectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', true);
      $("#selectAll").prop('checked', true);
    });
    
    $("#deselectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', false);
      $("#selectAll").prop('checked', false);
    });
    
    // 按食材分组
    $("#groupByIngredient").click(function(e) {
      e.preventDefault();
      groupTable("ingredient-id");
    });
    
    // 按批次分组
    $("#groupByBatch").click(function(e) {
      e.preventDefault();
      groupTable("batch-number");
    });
    
    // 分组函数
    function groupTable(groupBy) {
      var rows = $(".batch-row").detach();
      var groups = {};
      
      rows.each(function() {
        var key = $(this).data(groupBy);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(this);
      });
      
      var tbody = $("#batchTable tbody");
      tbody.empty();
      
      $.each(groups, function(key, groupRows) {
        var groupHeader = $("<tr>").addClass("table-secondary");
        var headerText = groupBy === "ingredient-id" ? 
          "食材: " + $(groupRows[0]).find("td:eq(1)").text().trim().split('\n')[0] + " (" + groupRows.length + "个批次)" :
          "批次: " + key + " (" + groupRows.length + "个食材)";
        
        groupHeader.append($("<td>").attr("colspan", 6).text(headerText));
        tbody.append(groupHeader);
        
        $.each(groupRows, function(i, row) {
          tbody.append(row);
        });
      });
    }
    
    // 自动计算总价
    function calculateTotalPrice(row) {
      var quantity = parseFloat($(row).find('input[name^="quantity_"]').val()) || 0;
      var unitPrice = parseFloat($(row).find('input[name^="unit_price_"]').val()) || 0;
      var totalPrice = quantity * unitPrice;
      
      // 移除旧的总价显示
      $(row).find('.total-price').remove();
      
      // 如果有总价，则显示
      if (totalPrice > 0) {
        var totalPriceElement = $('<div class="total-price mt-2">总价: ' + totalPrice.toFixed(2) + ' 元</div>');
        $(row).find('input[name^="unit_price_"]').after(totalPriceElement);
      }
    }
    
    // 为所有数量和单价输入框添加事件监听
    $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function() {
      var row = $(this).closest('tr');
      calculateTotalPrice(row);
    });
    
    // 初始计算所有行的总价
    $('.batch-row').each(function() {
      calculateTotalPrice(this);
    });
    
    // 表单提交前验证
    $('#batchEditForm').submit(function(e) {
      var selectedItems = $('.batch-checkbox:checked').length;
      
      if (selectedItems === 0) {
        e.preventDefault();
        alert('请至少选择一个批次进行编辑');
        return false;
      }
      
      var hasEmptyFields = false;
      
      $('.batch-checkbox:checked').each(function() {
        var itemId = $(this).val();
        var row = $(this).closest('tr');
        
        var supplier = row.find('select[name="supplier_id_' + itemId + '"]').val();
        var location = row.find('select[name="storage_location_id_' + itemId + '"]').val();
        var quantity = row.find('input[name="quantity_' + itemId + '"]').val();
        var unitPrice = row.find('input[name="unit_price_' + itemId + '"]').val();
        
        if (!supplier || !location || !quantity || !unitPrice) {
          hasEmptyFields = true;
          return false;
        }
      });
      
      if (hasEmptyFields) {
        e.preventDefault();
        alert('请为选中的批次填写完整的供应商、存储位置、数量和单价信息');
        return false;
      }
      
      return true;
    });
  });
</script>
{% endblock %}
