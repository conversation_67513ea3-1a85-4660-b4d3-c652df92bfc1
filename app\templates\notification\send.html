{% extends 'base.html' %}

{% block title %}发送通知 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-paper-plane"></i> 发送通知
                    </h3>
                    <a href="{{ url_for('notification.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回通知中心
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" id="sendNotificationForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label"><strong>通知标题 <span class="text-danger">*</span></strong></label>
                                    <input type="text" class="form-control" id="title" name="title" required maxlength="100" placeholder="请输入通知标题">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="notification_type" class="form-label"><strong>通知类型</strong></label>
                                    <select class="form-control" id="notification_type" name="notification_type">
                                        {% for type_code, type_name in notification_types.items() %}
                                        <option value="{{ type_code }}" {% if type_code == 'system' %}selected{% endif %}>{{ type_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="level" class="form-label"><strong>通知级别</strong></label>
                                    <select class="form-control" id="level" name="level">
                                        {% for level_code, level_name in notification_levels.items() %}
                                        <option value="{{ level_code }}" {% if level_code == 0 %}selected{% endif %}>{{ level_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 通知内容 -->
                        <div class="mb-3">
                            <label for="content" class="form-label"><strong>通知内容 <span class="text-danger">*</span></strong></label>
                            <textarea class="form-control" id="content" name="content" rows="4" required placeholder="请输入通知内容"></textarea>
                        </div>
                        
                        <!-- 发送方式 -->
                        <div class="mb-3">
                            <label class="form-label"><strong>发送方式</strong></label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="send_type" id="send_type_user" value="user" checked>
                                <label class="form-check-label" for="send_type_user">
                                    <i class="fas fa-user"></i> 发送给指定用户
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="send_type" id="send_type_area" value="area">
                                <label class="form-check-label" for="send_type_area">
                                    <i class="fas fa-building"></i> 发送给指定区域
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="send_type" id="send_type_all" value="all">
                                <label class="form-check-label" for="send_type_all">
                                    <i class="fas fa-globe"></i> 发送给所有用户
                                </label>
                            </div>
                        </div>
                        
                        <!-- 用户选择 -->
                        <div id="user_selection" class="mb-3">
                            <label class="form-label"><strong>选择用户 <span class="text-danger">*</span></strong></label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select_all_users">
                                        <label class="form-check-label" for="select_all_users">
                                            <strong>全选</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px;">
                                {% for user in users %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input user-checkbox" type="checkbox" name="user_ids" value="{{ user.id }}" id="user_{{ user.id }}">
                                        <label class="form-check-label" for="user_{{ user.id }}">
                                            {{ user.real_name or user.username }}
                                            {% if user.area %}
                                            <small class="text-muted">({{ user.area.name }})</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <!-- 区域选择 -->
                        <div id="area_selection" class="mb-3" style="display: none;">
                            <label for="area_id" class="form-label"><strong>选择区域 <span class="text-danger">*</span></strong></label>
                            <select class="form-control" id="area_id" name="area_id">
                                <option value="">请选择区域</option>
                                {% for area in areas %}
                                <option value="{{ area.id }}">{{ area.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" name="include_sub_areas" id="include_sub_areas" checked>
                                <label class="form-check-label" for="include_sub_areas">
                                    包含子区域
                                </label>
                            </div>
                            <div id="area_users_preview" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i> 
                                    将发送给该区域的 <span id="area_users_count">0</span> 个用户
                                </small>
                            </div>
                        </div>
                        
                        <!-- 全局发送提示 -->
                        <div id="all_selection" class="mb-3" style="display: none;">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>注意：</strong>将向系统中所有活跃用户发送通知，请谨慎操作。
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="mb-3 text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> 发送通知
                            </button>
                            <a href="{{ url_for('notification.index') }}" class="btn btn-secondary btn-lg ms-2">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 发送方式切换
    $('input[name="send_type"]').change(function() {
        var sendType = $(this).val();
        
        $('#user_selection').hide();
        $('#area_selection').hide();
        $('#all_selection').hide();
        
        if (sendType === 'user') {
            $('#user_selection').show();
        } else if (sendType === 'area') {
            $('#area_selection').show();
        } else if (sendType === 'all') {
            $('#all_selection').show();
        }
    });
    
    // 全选用户
    $('#select_all_users').change(function() {
        $('.user-checkbox').prop('checked', $(this).is(':checked'));
    });
    
    // 用户选择变化时更新全选状态
    $('.user-checkbox').change(function() {
        var totalUsers = $('.user-checkbox').length;
        var checkedUsers = $('.user-checkbox:checked').length;
        $('#select_all_users').prop('checked', totalUsers === checkedUsers);
    });
    
    // 区域选择变化时预览用户数量
    $('#area_id').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            $.get('/notifications/api/users/' + areaId)
                .done(function(data) {
                    if (data.success) {
                        $('#area_users_count').text(data.users.length);
                        $('#area_users_preview').show();
                    }
                })
                .fail(function() {
                    $('#area_users_preview').hide();
                });
        } else {
            $('#area_users_preview').hide();
        }
    });
    
    // 表单提交验证
    $('#sendNotificationForm').submit(function(e) {
        var sendType = $('input[name="send_type"]:checked').val();
        
        if (sendType === 'user') {
            var checkedUsers = $('.user-checkbox:checked').length;
            if (checkedUsers === 0) {
                e.preventDefault();
                alert('请至少选择一个用户');
                return false;
            }
        } else if (sendType === 'area') {
            var areaId = $('#area_id').val();
            if (!areaId) {
                e.preventDefault();
                alert('请选择一个区域');
                return false;
            }
        }
        
        // 确认发送
        var title = $('#title').val();
        var content = $('#content').val();
        var confirmMsg = '确定要发送通知"' + title + '"吗？';
        
        if (sendType === 'all') {
            confirmMsg = '确定要向所有用户发送通知"' + title + '"吗？此操作不可撤销！';
        }
        
        if (!confirm(confirmMsg)) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
