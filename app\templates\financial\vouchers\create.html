{% extends 'financial/base.html' %}

{% block title %}
{% if mode == 'view' %}
查看记账凭证 - {{ super() }}
{% elif mode == 'edit' %}
编辑记账凭证 - {{ super() }}
{% else %}
新建记账凭证 - {{ super() }}
{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<style nonce="{{ csp_nonce }}">
.voucher-container, .voucher-container * {
    font-size: 13px !important;
}
/* 用友风格科目选择器样式 */
.uf-subject-selector, .uf-subject-selector * {
    font-size: 13px !important;
}

.uf-subject-selector {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 800px;
    height: 600px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: var(--yonyou-font-family);
    font-size: 13px;
}

.uf-window {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.uf-window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(to bottom, #e6f7ff, #bae7ff);
    border-bottom: 1px solid #91d5ff;
    border-radius: 4px 4px 0 0;
}

.uf-window-title {
    display: flex;
    align-items: center;
    font-weight: bold;
    color: #1890ff;
    font-size: 13px;
}

.uf-window-title i {
    margin-right: 6px;
    font-size: 13px;
}

.uf-window-controls {
    display: flex;
    gap: 4px;
}

.uf-btn-minimize,
.uf-btn-close {
    width: 20px;
    height: 20px;
    border: none;
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.uf-btn-minimize:hover {
    background: #e6f7ff;
}

.uf-btn-close:hover {
    background: #ff4d4f;
    color: #fff;
}

.uf-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    gap: 8px;
    font-size: 13px;
}

.uf-toolbar-group {
    display: flex;
    gap: 4px;
}

.uf-toolbar-separator {
    width: 1px;
    height: 20px;
    background: #d9d9d9;
    margin: 0 8px;
}

.uf-btn {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.uf-btn:hover {
    border-color: #40a9ff;
    color: #1890ff;
}

.uf-btn-primary {
    background: #1890ff;
    color: #fff;
    border-color: #1890ff;
}

.uf-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

.uf-search-group {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.uf-search-input {
    width: 200px;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px 0 0 2px;
    font-size: 13px;
    outline: none;
}

.uf-search-input:focus {
    border-color: #40a9ff;
}

.uf-btn-search {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-start: none;
    background: #fff;
    border-radius: 0 2px 2px 0;
    cursor: pointer;
    transition: all 0.2s;
}

.uf-btn-search:hover {
    background: #e6f7ff;
    border-color: #40a9ff;
}

.uf-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.uf-subject-panel {
    display: flex;
    width: 100%;
    height: 100%;
}

.uf-subject-tree {
    width: 40%;
    border-end: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    font-size: 13px;
}

.uf-tree-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
    font-size: 13px;
    color: #666;
}

.uf-tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
}

.uf-tree-node {
    display: flex;
    align-items: center;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 13px;
    line-height: 20px;
    border-radius: 2px;
    transition: background 0.2s;
}

.uf-tree-node:hover {
    background: #e6f7ff;
}

.uf-tree-node.selected {
    background: #bae7ff;
    color: #1890ff;
}

.uf-tree-expand {
    width: 16px;
    text-align: center;
    cursor: pointer;
    user-select: none;
}

.uf-tree-icon {
    margin-right: 4px;
    font-size: 13px;
}

.uf-tree-text {
    flex: 1;
    cursor: pointer;
}

.uf-subject-details {
    width: 40%;
    display: flex;
    flex-direction: column;
}

.uf-details-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
    font-size: 13px;
    color: #666;
}

.uf-details-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
}

.uf-detail-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 13px;
}

.uf-detail-item label {
    width: 80px;
    color: #666;
    margin-right: 8px;
}

.uf-detail-item span {
    color: #222;
    font-weight: 500;
}

.uf-statusbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px;
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    font-size: 13px;
    color: #666;
}

.uf-no-results {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 13px;
}

.search-result {
    background: #fff7e6;
    border-start: 3px solid #faad14;
}

.search-result:hover {
    background: #fff1b8;
}

.uf-search-type-header {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: #f0f9ff;
    border-bottom: 1px solid #e6f7ff;
    margin: 2px 0;
    font-size: 13px;
}

.uf-type-group {
    background: #f6ffed;
    border-start: 3px solid #52c41a;
}

.uf-system-subject {
    background: #f6ffed;
}

.uf-school-subject {
    background: #f9f0ff;
}

/* 图标字体 */
.uf-icon-folder::before { content: "📁"; }
.uf-icon-check::before { content: "✓"; }
.uf-icon-close::before { content: "✕"; }
.uf-icon-expand::before { content: "⊞"; }
.uf-icon-collapse::before { content: "⊟"; }
.uf-icon-search::before { content: "��"; }
/* 用友风格专业凭证编辑器样式 - 与列表页面保持一致 */
.voucher-container {
    background: #f5f7fa;
    min-height: 100vh;
    padding: 10px;
}

.voucher-window {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
    margin: 0 auto;
    max-width: 1400px;
}

.voucher-header {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border-bottom: 1px solid #90caf9;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: bold;
    color: #1565c0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;
    padding: 5px 10px;
    display: flex;
    gap: 5px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.toolbar-btn.primary {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.toolbar-btn.primary:hover {
    background: linear-gradient(to bottom, #1565c0, #0d47a1);
    color: white;
}

.voucher-info-bar {
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    font-size: 13px;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    gap: 15px;
}

.info-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.info-label {
    color: #666;
    font-weight: normal;
    font-size: 13px;
    font-family: '宋体', 'SimSun', serif;
    white-space: nowrap;
}

.info-input {
    border: 1px solid #e0e0e0;
    padding: 2px 5px;
    font-size: 13px;
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    background: white;
    border-radius: 3px;
}

.info-input:focus {
    border-color: #333;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    outline: none;
}

/* 简洁的凭证字号样式 - 去除多余边框 */
.voucher-type-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.voucher-type-label {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
}

.voucher-type-select {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
    border: 1px solid #999;
    background: white;
    outline: none;
    min-width: 40px;
    padding: 2px 4px;
    border-radius: 2px;
}

.voucher-number-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.voucher-number-label {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
}

.voucher-number-input {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: 'Times New Roman', serif;
    border: 1px solid #999;
    background: white;
    outline: none;
    min-width: 120px;
    text-align: center;
    padding: 2px 4px;
    border-radius: 2px;
}

.voucher-table-container {
    padding: 0;
    background: white;
    overflow: auto;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin: 0;
    table-layout: fixed;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.voucher-table th {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    padding: 6px 4px;
    text-align: center;
    font-weight: normal;
    color: #1565c0;
    font-size: 13px;
    white-space: nowrap;
}

.voucher-table td {
    border: 1px solid #e0e0e0;
    padding: 6px 4px;
    vertical-align: middle;
    background: white;
    font-size: 13px;
    line-height: 1.6;
}

.voucher-table tbody tr:hover {
    background: #f5f5f5;
}

.voucher-table tbody tr.selected {
    background: #e3f2fd;
}

.cell-input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 4px;
    font-size: 13px;
    outline: none;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.cell-input:focus {
    background: #e3f2fd;
    border: 1px solid #1e88e5;
}

/* 摘要输入框支持换行 */
.summary-input {
    min-height: 40px;
    resize: vertical;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 金额输入框用友风格 - 与列表页面保持一致 */
.amount-input {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    font-weight: bold;
    text-align: right;
    font-size: 13px;
    color: #000;
    letter-spacing: 0.5px;
}

.line-number {
    text-align: center;
    color: #666;
    background: #f8f8f8;
    width: 30px;
    font-size: 13px;
}

.subject-cell {
    position: relative;
    min-width: 200px;
}

.subject-selector {
    display: flex;
    align-items: center;
    gap: 2px;
}

.subject-code {
    width: 60px;
    text-align: center;
    font-size: 13px;
}

.subject-name {
    flex: 1;
    font-size: 13px;
}

.subject-btn {
    background: #f0f0f0;
    border: 1px solid #e0e0e0;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 13px;
}

.totals-row {
    background: #e3f2fd;
    font-weight: bold;
    font-size: 13px;
}

.totals-row td {
    border-top: 2px solid #1e88e5;
    color: #1565c0;
}

.balance-status {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: bold;
}

.balance-ok {
    background: #e8f5e9;
    color: #2e7d32;
}

.balance-error {
    background: #ffebee;
    color: #c62828;
}

.signature-area {
    background: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    padding: 10px 15px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    font-size: 13px;
}

.signature-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.signature-label {
    color: #666;
    min-width: 40px;
}

.signature-box {
    border-bottom: 1px solid #999;
    min-width: 80px;
    height: 20px;
    display: inline-block;
}

/* 状态栏 - 与列表页面保持一致 */
.status-bar {
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 4px 15px;
    font-size: 13px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 用友经典货币符号 */
.uf-currency {
    font-family: 'Times New Roman', serif;
    font-weight: bold;
    color: #000;
}

/* 模式特定样式 */
.mode-view .cell-input,
.mode-view .info-input {
    border: none;
    background: transparent;
    pointer-events: none;
    color: #333;
}

.mode-view .toolbar-btn.edit-only {
    display: none;
}

.mode-view .toolbar-btn.view-only {
    display: inline-block;
}

.mode-edit .toolbar-btn.view-only {
    display: none;
}

.mode-edit .toolbar-btn.edit-only {
    display: inline-block;
}

/* 审核状态标签 */
.voucher-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: normal;
    margin-left: 10px;
}

.voucher-status.draft {
    background-color: #e0e0e0;
    color: #616161;
}

.voucher-status.pending {
    background-color: #fff8e1;
    color: #ff8f00;
}

.voucher-status.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.voucher-status.posted {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* 响应式调整 */
@d-flex (max-width: 768px) {
    .voucher-info-bar {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .signature-area {
        grid-template-columns: repeat(2, 1fr);
    }

    .voucher-table {
        font-size: 11px;
    }
}
</style>
{% endblock %}

{% block page_title %}
{% if mode == 'view' %}
查看记账凭证
{% elif mode == 'edit' %}
编辑记账凭证
{% else %}
新增记账凭证
{% endif %}
{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">
{% if mode == 'view' %}
查看凭证
{% elif mode == 'edit' %}
编辑凭证
{% else %}
新增凭证
{% endif %}
</span>
{% endblock %}

{% block financial_content %}
<div class="voucher-container mode-{{ mode|default('create') }}">
    <div class="voucher-window">
        <!-- 窗口标题栏 -->
        <div class="voucher-header">
            <span>记账凭证
                {% if voucher %}
                    <span class="voucher-status {{ voucher.status|lower }}">{{ voucher.status }}</span>
                {% endif %}
            </span>
            <div class="window-controls">
                {% if mode == 'view' %}
                <button class="toolbar-btn" onclick="location.href='{{ url_for('financial.edit_voucher', id=voucher.id) }}'">✏️ 编辑</button>
                {% endif %}
                {% if mode == 'edit' %}
                <button class="toolbar-btn" onclick="location.href='{{ url_for('financial.view_voucher', id=voucher.id) }}'">👁️ 查看</button>
                {% endif %}
                <button class="toolbar-btn" onclick="location.href='{{ url_for('financial.vouchers_index') }}'">📋 列表</button>
                <button class="toolbar-btn" onclick="window.history.back()">✕ 关闭</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="voucher-toolbar">
            {% if mode != 'view' %}
            <button class="toolbar-btn edit-only" onclick="saveVoucher()">💾 保存</button>
            <button class="toolbar-btn edit-only" onclick="addRow()">➕ 增行</button>
            <button class="toolbar-btn edit-only" onclick="deleteRow()">➖ 删行</button>
            <button class="toolbar-btn edit-only" onclick="insertRow()">📝 插行</button>
            <button class="toolbar-btn edit-only" onclick="copyRow()">📋 复制</button>
            {% endif %}
            <button class="toolbar-btn" onclick="checkBalance()">⚖️ 平衡</button>
            {% if mode == 'view' %}
            <button class="toolbar-btn view-only" onclick="printVoucher()">🖨️ 打印</button>
            <button class="toolbar-btn view-only" onclick="exportVoucher()">📤 导出</button>
            {% if voucher and voucher.status == '待审核' %}
            <button class="toolbar-btn view-only" onclick="reviewVoucher()">✅ 审核</button>
            {% endif %}
            {% endif %}
            <div style="margin-left: auto;">
                <span class="balance-status" id="balance-indicator">未检查</span>
            </div>
        </div>

        <!-- 凭证信息栏 -->
        <div class="voucher-info-bar">
            <!-- 凭证字 -->
            <div class="voucher-type-group">
                <span class="voucher-type-label">凭证字</span>
                <select class="voucher-type-select" id="voucher-type" {% if mode == 'view' %}disabled{% endif %}>
                    <option value="记" {% if voucher and voucher.voucher_type == '记' %}selected{% endif %}>记</option>
                    <option value="收" {% if voucher and voucher.voucher_type == '收' %}selected{% endif %}>收</option>
                    <option value="付" {% if voucher and voucher.voucher_type == '付' %}selected{% endif %}>付</option>
                    <option value="转" {% if voucher and voucher.voucher_type == '转' %}selected{% endif %}>转</option>
                </select>
            </div>

            <!-- 凭证号 -->
            <div class="voucher-number-group">
                <span class="voucher-number-label">号</span>
                <input type="text" class="voucher-number-input" id="voucher-number"
                       value="{% if voucher %}{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}{% else %}自动{% endif %}"
                       {% if mode == 'view' %}readonly{% endif %} placeholder="自动">
            </div>

            <!-- 日期 -->
            <div class="info-group">
                <span class="info-label">日期</span>
                <input type="date" class="info-input" id="voucher-date" style="width: 120px;"
                       value="{% if voucher %}{{ voucher.voucher_date }}{% else %}{{ today }}{% endif %}"
                       {% if mode == 'view' %}readonly{% endif %}>
            </div>

            <!-- 附件 -->
            <div class="info-group">
                <span class="info-label">附件</span>
                <input type="number" class="info-input" id="attachment-count" style="width: 40px;"
                       value="{% if voucher %}{{ voucher.attachment_count or 0 }}{% else %}0{% endif %}"
                       min="0" {% if mode == 'view' %}readonly{% endif %}>
                <span class="info-label">张</span>
            </div>
        </div>

        <!-- 凭证表格 -->
        <div class="voucher-table-container">
            <table class="voucher-table" id="voucher-table">
                <thead>
                    <tr>
                        <th style="width: 30px;">序号</th>
                        <th style="width: 200px;">摘要</th>
                        <th style="width: 250px;">会计科目</th>
                        <th style="width: 80px;">借方金额</th>
                        <th style="width: 80px;">贷方金额</th>
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                    {% if voucher and details %}
                        {% for detail in details %}
                        <tr data-row="{{ loop.index }}" data-detail-id="{{ detail.id }}">
                            <td class="line-number">{{ loop.index }}</td>
                            <td>
                                <textarea class="cell-input summary-input"
                                          {% if mode == 'view' %}readonly{% endif %}
                                          placeholder="摘要"
                                          rows="2">{{ detail.summary }}</textarea>
                            </td>
                            <td class="subject-cell">
                                <div class="subject-selector">
                                    <input type="text" class="cell-input subject-code"
                                           value="{{ detail.subject.code }}"
                                           {% if mode == 'view' %}readonly{% else %}readonly onclick="openSubjectModal(this)"{% endif %}
                                           placeholder="科目">
                                    <input type="text" class="cell-input subject-name"
                                           value="{{ detail.subject.name }}"
                                           readonly placeholder="科目名称">
                                    <input type="hidden" class="subject-id" value="{{ detail.subject.id }}">
                                </div>
                            </td>
                            <td>
                                <input type="number" class="cell-input amount-input debit-amount"
                                       value="{% if detail.debit_amount > 0 %}{{ detail.debit_amount }}{% endif %}"
                                       {% if mode == 'view' %}readonly{% endif %}
                                       step="0.01" min="0" placeholder="0.00">
                            </td>
                            <td>
                                <input type="number" class="cell-input amount-input credit-amount"
                                       value="{% if detail.credit_amount > 0 %}{{ detail.credit_amount }}{% endif %}"
                                       {% if mode == 'view' %}readonly{% endif %}
                                       step="0.01" min="0" placeholder="0.00">
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <!-- 新建模式下的空行将通过JavaScript动态生成 -->
                    {% endif %}
                </tbody>
                <tfoot>
                    <tr class="totals-row">
                        <td colspan="3" style="text-align: center; font-weight: bold; font-family: '宋体', 'SimSun', serif;">合计</td>
                        <td class="amount-cell" id="debit-total" style="font-family: 'Times New Roman', '宋体', 'SimSun', serif; font-weight: bold; text-align: right;"><span class="uf-currency">¥</span>0.00</td>
                        <td class="amount-cell" id="credit-total" style="font-family: 'Times New Roman', '宋体', 'SimSun', serif; font-weight: bold; text-align: right;"><span class="uf-currency">¥</span>0.00</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 签字区域 -->
        <div class="signature-area">
            <div class="signature-item">
                <span class="signature-label">制单:</span>
                <span class="signature-box">
                    {% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}
                </span>
            </div>
            <div class="signature-item">
                <span class="signature-label">审核:</span>
                <span class="signature-box">
                    {% if voucher and voucher.reviewed_by %}{{ voucher.reviewed_by.username }}{% endif %}
                </span>
            </div>
            <div class="signature-item">
                <span class="signature-label">记账:</span>
                <span class="signature-box">
                    {% if voucher and voucher.posted_by %}{{ voucher.posted_by.username }}{% endif %}
                </span>
            </div>
            <div class="signature-item">
                <span class="signature-label">出纳:</span>
                <span class="signature-box"></span>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>就绪</span>
            <span id="current-time"></span>
        </div>
    </div>
</div>

<!-- 用友风格科目选择窗口 -->
<div class="uf-subject-selector" id="subjectSelector" style="display: none;">
    <div class="uf-window">
        <!-- 窗口标题栏 -->
        <div class="uf-window-header">
            <div class="uf-window-title">
                <i class="uf-icon-folder"></i>
                <span>会计科目</span>
            </div>
            <div class="uf-window-controls">
                <button class="uf-btn-minimize" onclick="minimizeSubjectSelector()">_</button>
                <button class="uf-btn-close" onclick="closeSubjectSelector()">×</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="uf-toolbar">
            <div class="uf-toolbar-group">
                <button class="uf-btn uf-btn-primary" onclick="confirmSubjectSelection()">
                    <i class="uf-icon-check"></i> 确定
                </button>
                <button class="uf-btn" onclick="closeSubjectSelector()">
                    <i class="uf-icon-close"></i> 取消
                </button>
            </div>
            <div class="uf-toolbar-separator"></div>
            <div class="uf-toolbar-group">
                <button class="uf-btn" onclick="expandAllSubjects()">
                    <i class="uf-icon-expand"></i> 展开
                </button>
                <button class="uf-btn" onclick="collapseAllSubjects()">
                    <i class="uf-icon-collapse"></i> 折叠
                </button>
            </div>
            <div class="uf-toolbar-separator"></div>
            <div class="uf-search-group">
                <input type="text" class="uf-search-input" id="uf-subject-search"
                       placeholder="输入科目编码或名称进行搜索...">
                <button class="uf-btn-search" onclick="searchSubjects()">
                    <i class="uf-icon-search"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="uf-content">
            <div class="uf-subject-panel">
                <!-- 左侧科目树 -->
                <div class="uf-subject-tree">
                    <div class="uf-tree-header">
                        <span>科目结构</span>
                    </div>
                    <div class="uf-tree-content" id="subjectTree">
                        <!-- 科目树将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 右侧科目详情 -->
                <div class="uf-subject-details">
                    <div class="uf-details-header">
                        <span>科目信息</span>
                    </div>
                    <div class="uf-details-content">
                        <div class="uf-detail-item">
                            <label>科目编码：</label>
                            <span id="selectedSubjectCode">-</span>
                        </div>
                        <div class="uf-detail-item">
                            <label>科目名称：</label>
                            <span id="selectedSubjectName">-</span>
                        </div>
                        <div class="uf-detail-item">
                            <label>科目类型：</label>
                            <span id="selectedSubjectType">-</span>
                        </div>
                        <div class="uf-detail-item">
                            <label>科目级次：</label>
                            <span id="selectedSubjectLevel">-</span>
                        </div>
                        <div class="uf-detail-item">
                            <label>余额方向：</label>
                            <span id="selectedSubjectDirection">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="uf-statusbar">
            <span id="subjectCount">共 0 个科目</span>
            <span id="selectedSubjectInfo">未选择科目</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let currentRow = 0;
let currentCol = 0;
let subjects = [];
let selectedSubjectCell = null;
let voucherMode = '{{ mode|default("create") }}';
let voucherId = {% if voucher %}{{ voucher.id }}{% else %}null{% endif %};

$(document).ready(function() {
    console.log('🚀 页面加载完成，初始化用友风格凭证编辑器...');

    // 优先加载会计科目数据
    console.log('📊 开始加载会计科目数据...');
    loadSubjects();

    // 初始化编辑器
    initVoucherEditor();

    // 更新时间
    updateTime();
    setInterval(updateTime, 1000);

    // 绑定事件
    bindEvents();

    // 根据模式初始化
    if (voucherMode === 'create' && $('#voucher-tbody tr').length === 0) {
        // 新建模式且没有现有行时，添加第一行
        addRow();
    } else if (voucherMode === 'view' || voucherMode === 'edit') {
        // 查看或编辑模式，更新合计
        updateTotals();
        checkBalance();
    }
});

function initVoucherEditor() {
    console.log('初始化用友风格凭证编辑器');

    // 设置今天日期
    const today = new Date().toISOString().split('T')[0];
    $('#voucher-date').val(today);
}

function bindEvents() {
    // 键盘导航
    $(document).on('keydown', '.cell-input', function(e) {
        handleKeyNavigation(e, this);
    });

    // 金额输入事件
    $(document).on('input', '.amount-input', function() {
        updateTotals();
        checkBalance();
    });

    // 用友风格科目搜索
    $('#uf-subject-search').on('input', function() {
        searchSubjects();
    });

    $('#uf-subject-search').on('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchSubjects();
        }
    });

    // 快捷键
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveVoucher();
        }
        if (e.key === 'F9') {
            e.preventDefault();
            checkBalance();
        }
    });
}

function handleKeyNavigation(e, element) {
    const $element = $(element);
    const $row = $element.closest('tr');
    const $cell = $element.closest('td');
    const rowIndex = $row.index();
    const cellIndex = $cell.index();

    switch(e.key) {
        case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
                moveToPreviousCell(rowIndex, cellIndex);
            } else {
                moveToNextCell(rowIndex, cellIndex);
            }
            break;
        case 'Enter':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
        case 'ArrowUp':
            e.preventDefault();
            moveToPreviousRow(rowIndex, cellIndex);
            break;
        case 'ArrowDown':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
    }
}

function moveToNextCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex < 4) { // 移动到下一列
        const $nextCell = $currentRow.find('td').eq(cellIndex + 1).find('.cell-input');
        if ($nextCell.length) {
            $nextCell.focus().select();
        }
    } else { // 移动到下一行第一列
        moveToNextRow(rowIndex, 0);
    }
}

function moveToPreviousCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex > 1) { // 移动到上一列
        const $prevCell = $currentRow.find('td').eq(cellIndex - 1).find('.cell-input');
        if ($prevCell.length) {
            $prevCell.focus().select();
        }
    } else if (rowIndex > 0) { // 移动到上一行最后一列
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $lastCell = $prevRow.find('td').eq(4).find('.cell-input');
        if ($lastCell.length) {
            $lastCell.focus().select();
        }
    }
}

function moveToNextRow(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    let $nextRow = $tbody.find('tr').eq(rowIndex + 1);

    if ($nextRow.length === 0) {
        // 如果没有下一行，自动添加新行
        addRow();
        $nextRow = $tbody.find('tr').last();
    }

    const targetCellIndex = Math.max(1, cellIndex); // 至少从摘要列开始
    const $targetCell = $nextRow.find('td').eq(targetCellIndex).find('.cell-input');
    if ($targetCell.length) {
        $targetCell.focus().select();
    }
}

function moveToPreviousRow(rowIndex, cellIndex) {
    if (rowIndex > 0) {
        const $tbody = $('#voucher-tbody');
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $targetCell = $prevRow.find('td').eq(cellIndex).find('.cell-input');
        if ($targetCell.length) {
            $targetCell.focus().select();
        }
    }
}

function addRow() {
    if (voucherMode === 'view') {
        return; // 查看模式不允许添加行
    }

    const $tbody = $('#voucher-tbody');
    const rowNumber = $tbody.find('tr').length + 1;

    const rowHtml = `
        <tr data-row="${rowNumber}">
            <td class="line-number">${rowNumber}</td>
            <td>
                <textarea class="cell-input summary-input" placeholder="摘要" rows="2"></textarea>
            </td>
            <td class="subject-cell">
                <div class="subject-selector">
                    <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                    <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                    <input type="hidden" class="subject-id">
                </div>
            </td>
            <td>
                <input type="number" class="cell-input amount-input debit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
            <td>
                <input type="number" class="cell-input amount-input credit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
        </tr>
    `;

    $tbody.append(rowHtml);
    updateRowNumbers();
}

function deleteRow() {
    if (voucherMode === 'view') {
        return; // 查看模式不允许删除行
    }

    const $tbody = $('#voucher-tbody');
    const $rows = $tbody.find('tr');

    if ($rows.length > 1) {
        $rows.last().remove();
        updateRowNumbers();
        updateTotals();
        checkBalance();
    }
}

function insertRow() {
    // 在当前焦点行之后插入新行
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const rowNumber = $currentRow.index() + 1;

        const rowHtml = `
            <tr data-row="${rowNumber}">
                <td class="line-number">${rowNumber}</td>
                <td>
                    <textarea class="cell-input summary-input" placeholder="摘要" rows="2"></textarea>
                </td>
                <td class="subject-cell">
                    <div class="subject-selector">
                        <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                        <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                        <input type="hidden" class="subject-id">
                    </div>
                </td>
                <td>
                    <input type="number" class="cell-input amount-input debit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td>
                    <input type="number" class="cell-input amount-input credit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
            </tr>
        `;

        $currentRow.after(rowHtml);
        updateRowNumbers();
    } else {
        addRow();
    }
}

function copyRow() {
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const $newRow = $currentRow.clone();

        // 清空新行的序号和ID
        $newRow.find('.line-number').text('');
        $newRow.removeAttr('data-row');

        $currentRow.after($newRow);
        updateRowNumbers();
    }
}

function updateRowNumbers() {
    $('#voucher-tbody tr').each(function(index) {
        $(this).find('.line-number').text(index + 1);
        $(this).attr('data-row', index + 1);
    });
}

function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.debit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        debitTotal += value;
    });

    $('.credit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        creditTotal += value;
    });

    $('#debit-total').html('<span class="uf-currency">¥</span>' + debitTotal.toFixed(2));
    $('#credit-total').html('<span class="uf-currency">¥</span>' + creditTotal.toFixed(2));
}

function checkBalance() {
    // 从包含货币符号的文本中提取数字
    const debitText = $('#debit-total').text().replace(/[¥,]/g, '');
    const creditText = $('#credit-total').text().replace(/[¥,]/g, '');
    const debitTotal = parseFloat(debitText) || 0;
    const creditTotal = parseFloat(creditText) || 0;
    const difference = Math.abs(debitTotal - creditTotal);

    const $indicator = $('#balance-indicator');

    if (difference < 0.01) {
        $indicator.removeClass('balance-error').addClass('balance-ok').text('借贷平衡');
    } else {
        $indicator.removeClass('balance-ok').addClass('balance-error').text(`不平衡 差额:¥${difference.toFixed(2)}`);
    }
}

function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    $('#current-time').text(timeString);
}

function loadSubjects() {
    console.log('开始加载会计科目数据...');

    $.ajax({
        url: '{{ url_for("financial.accounting_subjects_api") }}',
        type: 'GET',
        dataType: 'json',
        timeout: 10000,
        success: function(response) {
            console.log('API响应:', response);

            // 适配现有API的返回格式（直接返回数组）
            if (Array.isArray(response) && response.length > 0) {
                subjects = response;
                console.log(`✅ 科目数据加载成功: ${subjects.length} 个科目`);

                // 按科目类型分组显示统计
                const typeStats = {};
                subjects.forEach(subject => {
                    const type = subject.subject_type || '其他';
                    typeStats[type] = (typeStats[type] || 0) + 1;
                });
                console.log('科目类型统计:', typeStats);

                // 显示部分科目数据用于调试
                if (subjects.length > 0) {
                    console.log('前5个科目示例:', subjects.slice(0, 5));
                }

                // 构建科目树并渲染
                buildSubjectTree();
                renderSubjectTree();
                updateSubjectStatusBar();

            } else if (response && response.success === false) {
                console.error('❌ 加载科目失败:', response.message || '未知错误');
                subjects = [];
                alert('加载会计科目失败: ' + (response.message || '请检查网络连接'));
            } else {
                console.warn('⚠️ 未获取到科目数据，可能是空数组');
                subjects = [];
                alert('未获取到会计科目数据，请检查数据库中是否有科目数据');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ AJAX请求失败:');
            console.error('状态:', status);
            console.error('错误:', error);
            console.error('响应文本:', xhr.responseText);

            subjects = [];
            alert('网络错误，无法加载会计科目: ' + error);
        }
    });
}

// 用友风格科目选择器相关变量
let selectedSubject = null;
let subjectTree = [];
let filteredSubjects = [];

function openSubjectModal(element) {
    selectedSubjectCell = $(element).closest('.subject-selector');

    // 显示用友风格科目选择器
    $('#subjectSelector').show();

    // 构建科目树
    buildSubjectTree();

    // 渲染科目树
    renderSubjectTree();

    // 更新状态栏
    updateSubjectStatusBar();

    // 聚焦搜索框
    $('#uf-subject-search').focus();
}

function buildSubjectTree() {
    console.log('构建用友风格科目树，科目数量:', subjects.length);

    if (!subjects || subjects.length === 0) {
        console.warn('⚠️ 没有科目数据，无法构建科目树');
        subjectTree = [];
        return;
    }

    // 按科目编码排序
    const sortedSubjects = [...subjects].sort((a, b) => a.code.localeCompare(b.code));

    // 构建用友风格树形结构
    subjectTree = [];
    const subjectMap = new Map();

    // 按科目类型分组
    const typeGroups = {
        '资产': [],
        '负债': [],
        '所有者权益': [],
        '收入': [],
        '费用': [],
        '成本': [],
        '其他': []
    };

    sortedSubjects.forEach(subject => {
        const level = subject.level || getSubjectLevel(subject.code);
        const parentCode = getParentSubjectCode(subject.code);
        const subjectType = subject.subject_type || '其他';

        const treeNode = {
            ...subject,
            level: level,
            children: [],
            expanded: level <= 2, // 默认展开前两级
            parent: parentCode,
            isSystemSubject: subject.is_system,
            displayText: `${subject.code} ${subject.name}`,
            typeGroup: subjectType,
            display_name: `${subject.code} ${subject.name}`,
            balance_direction: subject.balance_direction || getBalanceDirection(subject.subject_type)
        };

        subjectMap.set(subject.code, treeNode);

        // 按类型分组
        if (typeGroups[subjectType]) {
            typeGroups[subjectType].push(treeNode);
        } else {
            typeGroups['其他'].push(treeNode);
        }
    });

    // 构建层级关系
    subjectMap.forEach(node => {
        if (node.parent && subjectMap.has(node.parent)) {
            subjectMap.get(node.parent).children.push(node);
        } else {
            // 顶级科目
            subjectTree.push(node);
        }
    });

    // 按科目类型重新组织（用友风格）
    const organizedTree = [];
    Object.keys(typeGroups).forEach(type => {
        if (typeGroups[type].length > 0) {
            // 创建类型分组节点
            const typeNode = {
                id: `type_${type}`,
                code: '',
                name: `${type}类科目`,
                level: 0,
                children: typeGroups[type].filter(node => node.level === 1),
                expanded: true,
                isTypeGroup: true,
                typeGroup: type
            };
            organizedTree.push(typeNode);
        }
    });

    subjectTree = organizedTree;
    console.log('✅ 科目树构建完成，顶级节点数:', subjectTree.length);
}

function getSubjectLevel(code) {
    // 根据科目编码长度判断级次
    if (code.length <= 4) return 1;
    if (code.length <= 6) return 2;
    if (code.length <= 8) return 3;
    return 4;
}

function getParentSubjectCode(code) {
    if (code.length <= 4) return null;
    if (code.length <= 6) return code.substring(0, 4);
    if (code.length <= 8) return code.substring(0, 6);
    return code.substring(0, 8);
}

function renderSubjectTree() {
    const $treeContent = $('#subjectTree');
    $treeContent.empty();

    subjectTree.forEach(node => {
        renderTreeNode(node, $treeContent, 0);
    });
}

function renderTreeNode(node, container, depth) {
    const indent = depth * 20;
    const hasChildren = node.children && node.children.length > 0;
    const expandIcon = hasChildren ? (node.expanded ? '▼' : '▶') : '　';

    // 用友风格：区分类型分组和科目节点
    let nodeClass = 'uf-tree-node';
    let iconClass = '📁';
    let textStyle = '';

    if (node.isTypeGroup) {
        // 科目类型分组
        nodeClass += ' uf-type-group';
        iconClass = '📂';
        textStyle = 'font-weight: bold; color: #1890ff;';
    } else if (node.isSystemSubject) {
        // 系统科目
        nodeClass += ' uf-system-subject';
        iconClass = '🏛️';
        textStyle = 'color: #52c41a;';
    } else {
        // 学校自定义科目
        nodeClass += ' uf-school-subject';
        iconClass = '🏫';
        textStyle = 'color: #722ed1;';
    }

    const nodeId = node.isTypeGroup ? node.id : node.id;
    const nodeCode = node.isTypeGroup ? '' : node.code;
    const displayText = node.isTypeGroup ? node.name : `${node.code} ${node.name}`;

    const nodeHtml = `
        <div class="${nodeClass}"
             data-subject-id="${nodeId}"
             data-subject-code="${nodeCode}"
             data-level="${node.level}"
             style="padding-left: ${indent}px;">
            <span class="uf-tree-expand" onclick="toggleTreeNode('${nodeCode || nodeId}')">${expandIcon}</span>
            <span class="uf-tree-icon">${iconClass}</span>
            <span class="uf-tree-text"
                  onclick="${node.isTypeGroup ? '' : `selectTreeSubject('${nodeCode}')`}"
                  style="${textStyle}"
                  title="${node.isTypeGroup ? '科目分组' : (node.isSystemSubject ? '系统科目' : '学校科目')}">
                ${displayText}
            </span>
        </div>
    `;

    container.append(nodeHtml);

    if (hasChildren && node.expanded) {
        node.children.forEach(child => {
            renderTreeNode(child, container, depth + 1);
        });
    }
}

function toggleTreeNode(code) {
    const node = findNodeByCode(subjectTree, code);
    if (node && node.children.length > 0) {
        node.expanded = !node.expanded;
        renderSubjectTree();
    }
}

function findNodeByCode(nodes, code) {
    for (let node of nodes) {
        if (node.code === code) return node;
        if (node.children) {
            const found = findNodeByCode(node.children, code);
            if (found) return found;
        }
    }
    return null;
}

function selectTreeSubject(code) {
    console.log('选择科目:', code);

    const subject = subjects.find(s => s.code === code);
    if (subject) {
        selectedSubject = subject;
        updateSubjectDetails(subject);

        // 高亮选中的节点
        $('.uf-tree-node').removeClass('selected');
        $(`.uf-tree-node[data-subject-code="${code}"]`).addClass('selected');

        console.log('✅ 已选择科目:', subject.display_name || `${subject.code} ${subject.name}`);
    } else {
        console.warn('⚠️ 未找到科目:', code);
    }
}

function updateSubjectDetails(subject) {
    $('#selectedSubjectCode').text(subject.code || '-');
    $('#selectedSubjectName').text(subject.name || '-');
    $('#selectedSubjectType').text(subject.subject_type || '-');
    $('#selectedSubjectLevel').text(subject.level || getSubjectLevel(subject.code));
    $('#selectedSubjectDirection').text(subject.balance_direction || getBalanceDirection(subject.subject_type));

    // 显示科目来源
    const sourceText = subject.is_system ? '系统科目' : '学校科目';
    const infoText = `已选择: ${subject.code} ${subject.name} (${sourceText})`;
    $('#selectedSubjectInfo').text(infoText);

    console.log('更新科目详情:', {
        code: subject.code,
        name: subject.name,
        type: subject.subject_type,
        level: subject.level,
        direction: subject.balance_direction,
        isSystem: subject.is_system
    });
}

function getBalanceDirection(subjectType) {
    const directions = {
        '资产': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方',
        '费用': '借方',
        '成本': '借方'
    };
    return directions[subjectType] || '-';
}

function expandAllSubjects() {
    expandCollapseAll(subjectTree, true);
    renderSubjectTree();
}

function collapseAllSubjects() {
    expandCollapseAll(subjectTree, false);
    renderSubjectTree();
}

function expandCollapseAll(nodes, expand) {
    nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
            node.expanded = expand;
            expandCollapseAll(node.children, expand);
        }
    });
}

function searchSubjects() {
    const searchTerm = $('#uf-subject-search').val().trim();
    console.log('搜索科目:', searchTerm);

    if (!searchTerm) {
        console.log('清空搜索，恢复科目树');
        renderSubjectTree();
        updateSubjectStatusBar();
        return;
    }

    // 用友风格搜索：支持编码、名称、拼音首字母
    filteredSubjects = subjects.filter(subject => {
        const code = (subject.code || '').toLowerCase();
        const name = (subject.name || '').toLowerCase();
        const type = (subject.subject_type || '').toLowerCase();
        const search = searchTerm.toLowerCase();

        return code.includes(search) ||
               name.includes(search) ||
               type.includes(search) ||
               (subject.display_name || '').toLowerCase().includes(search);
    });

    console.log(`搜索到 ${filteredSubjects.length} 个匹配的科目`);

    // 渲染搜索结果
    renderSearchResults();
}

function renderSearchResults() {
    const $treeContent = $('#subjectTree');
    $treeContent.empty();

    if (filteredSubjects.length === 0) {
        $treeContent.append('<div class="uf-no-results">未找到匹配的科目</div>');
        updateSubjectStatusBar();
        return;
    }

    // 按科目类型分组显示搜索结果
    const typeGroups = {};
    filteredSubjects.forEach(subject => {
        const type = subject.subject_type || '其他';
        if (!typeGroups[type]) {
            typeGroups[type] = [];
        }
        typeGroups[type].push(subject);
    });

    Object.keys(typeGroups).forEach(type => {
        // 类型分组标题
        const typeHeaderHtml = `
            <div class="uf-search-type-header">
                <span class="uf-tree-icon">📂</span>
                <span style="font-weight: bold; color: #1890ff;">${type}类 (${typeGroups[type].length})</span>
            </div>
        `;
        $treeContent.append(typeHeaderHtml);

        // 该类型下的科目
        typeGroups[type].forEach(subject => {
            const iconClass = subject.is_system ? '🏛️' : '🏫';
            const sourceText = subject.is_system ? '系统' : '学校';
            const nodeHtml = `
                <div class="uf-tree-node search-result"
                     data-subject-id="${subject.id}"
                     data-subject-code="${subject.code}"
                     style="padding-left: 20px;">
                    <span class="uf-tree-icon">${iconClass}</span>
                    <span class="uf-tree-text" onclick="selectTreeSubject('${subject.code}')">
                        ${subject.code} ${subject.name}
                        <small style="color: #999; margin-left: 8px;">[${sourceText}]</small>
                    </span>
                </div>
            `;
            $treeContent.append(nodeHtml);
        });
    });

    updateSubjectStatusBar();
}

function updateSubjectStatusBar() {
    const totalCount = subjects.length;
    const displayCount = filteredSubjects.length || totalCount;
    $('#subjectCount').text(`共 ${displayCount} 个科目`);
}

function confirmSubjectSelection() {
    if (selectedSubject && selectedSubjectCell) {
        selectedSubjectCell.find('.subject-code').val(selectedSubject.code);
        selectedSubjectCell.find('.subject-name').val(selectedSubject.name);
        selectedSubjectCell.find('.subject-id').val(selectedSubject.id);

        closeSubjectSelector();

        // 移动到下一个输入框
        const $nextInput = selectedSubjectCell.closest('td').next().find('.cell-input');
        if ($nextInput.length) {
            $nextInput.focus().select();
        }
    }
}

function closeSubjectSelector() {
    $('#subjectSelector').hide();
    selectedSubject = null;
    filteredSubjects = [];
    $('#uf-subject-search').val('');
}

function minimizeSubjectSelector() {
    // 最小化功能（可选实现）
    $('#subjectSelector .uf-content').toggle();
}

function saveVoucher() {
    const voucherData = collectVoucherData();

    if (!validateVoucherData(voucherData)) {
        return;
    }

    const url = voucherMode === 'edit' ?
        '{{ url_for("financial.update_voucher_api", id=999999) }}'.replace('999999', voucherId) :
        '{{ url_for("financial.create_voucher") }}';
    const method = voucherMode === 'edit' ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(voucherData),
        success: function(response) {
            if (response.success) {
                alert('凭证保存成功！');
                const targetId = response.voucher_id || voucherId;
                window.location.href = '{{ url_for("financial.view_voucher", id=999999) }}'.replace('999999', targetId);
            } else {
                alert('保存失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('保存失败，请重试');
            console.error('保存失败:', error);
        }
    });
}

function collectVoucherData() {
    const details = [];

    $('#voucher-tbody tr').each(function() {
        const $row = $(this);
        const summary = $row.find('.summary-input').val();
        const subjectId = $row.find('.subject-id').val();
        const debitAmount = parseFloat($row.find('.debit-amount').val()) || 0;
        const creditAmount = parseFloat($row.find('.credit-amount').val()) || 0;

        if (summary || subjectId || debitAmount > 0 || creditAmount > 0) {
            details.push({
                summary: summary,
                subject_id: subjectId,
                debit_amount: debitAmount,
                credit_amount: creditAmount
            });
        }
    });

    return {
        voucher_type: $('#voucher-type').val(),
        voucher_date: $('#voucher-date').val(),
        attachment_count: parseInt($('#attachment-count').val()) || 0,
        details: details
    };
}

function validateVoucherData(data) {
    if (data.details.length === 0) {
        alert('请至少添加一行凭证明细');
        return false;
    }

    let debitTotal = 0;
    let creditTotal = 0;

    for (let detail of data.details) {
        if (!detail.subject_id) {
            alert('请为所有明细行选择会计科目');
            return false;
        }

        if (detail.debit_amount === 0 && detail.credit_amount === 0) {
            alert('每行明细必须有借方或贷方金额');
            return false;
        }

        if (detail.debit_amount > 0 && detail.credit_amount > 0) {
            alert('每行明细不能同时有借方和贷方金额');
            return false;
        }

        debitTotal += detail.debit_amount;
        creditTotal += detail.credit_amount;
    }

    if (Math.abs(debitTotal - creditTotal) > 0.01) {
        alert('借贷不平衡，请检查金额');
        return false;
    }

    return true;
}

// 查看模式专用函数
function printVoucher() {
    if (voucherId) {
        window.open('{{ url_for("financial.voucher_text_view", id=999999) }}'.replace('999999', voucherId), '_blank');
    }
}

function exportVoucher() {
    if (voucherId) {
        window.location.href = '{{ url_for("financial.voucher_text_view", id=999999) }}'.replace('999999', voucherId);
    }
}

function reviewVoucher() {
    if (voucherId && confirm('确定要审核通过这张凭证吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("financial.review_voucher", id=999999) }}'.replace('999999', voucherId);
        document.body.appendChild(form);
        form.submit();
    }
}


</script>
{% endblock %}
