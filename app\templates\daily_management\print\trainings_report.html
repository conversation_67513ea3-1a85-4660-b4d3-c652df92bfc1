{% extends 'print_base.html' %}

{% block title %}培训记录汇总{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .report-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .report-header h1 {
        font-size: 24px;
        margin-bottom: 10px;
    }
    
    .report-header p {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }
    
    .report-meta {
        margin-bottom: 20px;
        font-size: 14px;
    }
    
    .report-meta-item {
        margin-bottom: 5px;
    }
    
    .report-meta-label {
        font-weight: bold;
        display: inline-block;
        width: 100px;
    }
    
    .training-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }
    
    .training-header {
        font-size: 18px;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #ddd;
    }
    
    .training-meta {
        margin-bottom: 15px;
    }
    
    .training-meta-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 5px;
    }
    
    .training-meta-item {
        flex: 1;
        min-width: 200px;
        margin-bottom: 5px;
    }
    
    .training-meta-label {
        font-weight: bold;
        margin-right: 5px;
    }
    
    .content-section {
        margin-top: 10px;
    }
    
    .content-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .content-box {
        padding: 10px;
        background-color: #f9f9f9;
        border: 1px solid #eee;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    
    .no-records {
        text-align: center;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 4px;
        font-style: italic;
        color: #666;
    }
    
    .summary-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ddd;
    }
    
    .summary-title {
        font-size: 18px;
        margin-bottom: 10px;
    }
    
    .summary-content {
        display: flex;
        flex-wrap: wrap;
    }
    
    .summary-item {
        flex: 1;
        min-width: 150px;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .summary-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .summary-value {
        font-size: 18px;
    }
    
    .print-footer {
        margin-top: 30px;
        text-align: right;
        font-size: 12px;
        color: #666;
    }
    
    @d-flex print {
        body {
            font-size: 12px;
        }
        
        .report-header h1 {
            font-size: 20px;
        }
        
        .training-header {
            font-size: 16px;
        }
        
        .summary-title {
            font-size: 16px;
        }
        
        .summary-value {
            font-size: 16px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="report-header">
    <h1>{{ log.area.name if log.area else '学校' }} - 培训记录汇总</h1>
    <p>日期：{{ log.log_date }}</p>
</div>

<div class="report-meta">
    <div class="report-meta-item">
        <span class="report-meta-label">管理员：</span>
        <span>{{ log.manager or '未设置' }}</span>
    </div>
</div>

{% if trainings %}
    <div class="summary-section">
        <div class="summary-title">培训统计</div>
        <div class="summary-content">
            <div class="summary-item">
                <div class="summary-label">培训总数</div>
                <div class="summary-value">{{ trainings|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">参训总人数</div>
                <div class="summary-value">{{ trainings|sum(attribute='trainee_count') }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">食品安全培训</div>
                <div class="summary-value">{{ trainings|selectattr('training_type', 'equalto', 'food_safety')|list|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">卫生管理培训</div>
                <div class="summary-value">{{ trainings|selectattr('training_type', 'equalto', 'hygiene')|list|length }}</div>
            </div>
        </div>
    </div>

    {% for training in trainings %}
    <div class="training-section">
        <div class="training-header">培训记录 #{{ loop.index }} - {{ training.training_topic }}</div>
        
        <div class="training-meta">
            <div class="training-meta-row">
                <div class="training-meta-item">
                    <span class="training-meta-label">培训时间：</span>
                    <span>{{ training.training_time|format_datetime if training.training_time else '未设置' }}</span>
                </div>
                <div class="training-meta-item">
                    <span class="training-meta-label">培训地点：</span>
                    <span>{{ training.training_location or '未设置' }}</span>
                </div>
            </div>
            
            <div class="training-meta-row">
                <div class="training-meta-item">
                    <span class="training-meta-label">培训人：</span>
                    <span>{{ training.trainer or '未设置' }}</span>
                </div>
                <div class="training-meta-item">
                    <span class="training-meta-label">参训人数：</span>
                    <span>{{ training.trainee_count or 0 }} 人</span>
                </div>
            </div>
            
            <div class="training-meta-row">
                <div class="training-meta-item">
                    <span class="training-meta-label">培训类型：</span>
                    <span>
                        {% if training.training_type == 'food_safety' %}
                            食品安全
                        {% elif training.training_type == 'hygiene' %}
                            卫生管理
                        {% elif training.training_type == 'operation' %}
                            操作规范
                        {% elif training.training_type == 'emergency' %}
                            应急处理
                        {% else %}
                            其他
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <div class="content-label">培训内容：</div>
            <div class="content-box">{{ training.training_content|nl2br }}</div>
        </div>
        
        {% if training.training_effect %}
        <div class="content-section">
            <div class="content-label">培训效果：</div>
            <div class="content-box">{{ training.training_effect|nl2br }}</div>
        </div>
        {% endif %}
        
        {% if training.training_attendees %}
        <div class="content-section">
            <div class="content-label">参训人员：</div>
            <div class="content-box">{{ training.training_attendees|nl2br }}</div>
        </div>
        {% endif %}
    </div>
    {% endfor %}
{% else %}
    <div class="no-records">
        <p>暂无培训记录</p>
    </div>
{% endif %}

<div class="print-footer">
    <p>打印时间：{{ print_date|format_datetime }}</p>
</div>
{% endblock %}
