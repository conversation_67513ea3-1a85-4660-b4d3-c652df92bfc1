{% extends 'base.html' %}

{% block title %}创建采购订单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .ingredient-row {
    margin-bottom: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #ffffff;
  }
  .ingredient-row:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
  }
  .supplier-select {
    width: 100%;
  }
  .quantity-input, .unit-price-input {
    width: 100%;
  }

  /* 修复颜色问题，使用系统主题 */
  .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .card-header h6 {
    color: #495057 !important;
  }

  .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #ffffff;
  }

  .btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
  }

  .form-label {
    color: #495057;
    font-weight: 500;
  }

  .text-primary {
    color: #007bff !important;
  }

  /* 隐藏区域选择字段 */
  .area-field-hidden {
    display: none;
  }

  /* 食材选择模态框样式 */
  .ingredient-tabs-container {
    max-height: 500px;
    overflow-y: auto;
  }

  /* 标签页导航样式 */
  .nav-pills .nav-link {
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 0.9rem;
    margin: 0 2px;
  }

  .nav-pills .nav-link.active {
    background-color: #007bff;
  }

  .nav-pills .nav-link:not(.active) {
    color: #6c757d;
    background-color: #f8f9fa;
  }

  .nav-pills .nav-link:not(.active):hover {
    background-color: #e9ecef;
    color: #495057;
  }

  /* 食材网格布局 */
  .ingredients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
    max-height: 350px;
    overflow-y: auto;
    padding: 5px;
  }

  .ingredient-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .ingredient-item:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
  }

  .ingredient-item .custom-control {
    margin: 0;
  }

  .ingredient-item .custom-control-label {
    cursor: pointer;
    width: 100%;
    padding-left: 0;
    margin: 0;
    position: relative;
  }

  .ingredient-item .custom-control-label::before,
  .ingredient-item .custom-control-label::after {
    position: absolute;
    top: 2px;
    left: 0;
  }

  .ingredient-item .custom-control-input:checked ~ .custom-control-label {
    color: #007bff;
  }

  .ingredient-item .custom-control-input:checked ~ .custom-control-label .ingredient-info strong {
    color: #007bff;
  }

  .ingredient-info {
    padding-left: 1.8rem;
    min-height: 20px;
  }

  .ingredient-info strong {
    font-size: 0.9rem;
    display: block;
    margin-bottom: 2px;
  }

  .ingredient-info small {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  /* 标签和按钮样式 */
  .badge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .select-all-ingredients,
  .select-category-all {
    font-size: 0.8rem;
    padding: 4px 8px;
  }

  /* 模态框大小调整 */
  .modal-lg {
    max-width: 800px;
  }

  /* 搜索框样式 */
  #ingredient-search {
    border-radius: 20px;
    padding-left: 15px;
  }

  /* 标签页内容区域 */
  .tab-content {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background-color: #fafafa;
  }

  /* 滚动条样式 */
  .ingredients-grid::-webkit-scrollbar,
  .ingredient-tabs-container::-webkit-scrollbar {
    width: 6px;
  }

  .ingredients-grid::-webkit-scrollbar-track,
  .ingredient-tabs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .ingredients-grid::-webkit-scrollbar-thumb,
  .ingredient-tabs-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .ingredients-grid::-webkit-scrollbar-thumb:hover,
  .ingredient-tabs-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 屏幕阅读器专用样式 */
  .visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }

  /* 供应商食材项目样式 */
  .ingredient-item.supplier-item {
    border-start: 3px solid #28a745;
    background: linear-gradient(to right, rgba(40, 167, 69, 0.05), transparent);
  }

  .ingredient-item.supplier-item:hover {
    background: linear-gradient(to right, rgba(40, 167, 69, 0.1), transparent);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
  }

  .badge-sm {
    font-size: 0.7em;
    padding: 0.2em 0.4em;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>创建采购订单</h2>
      <p class="text-muted">填写采购订单信息并提交</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 fw-bold text-primary">采购订单信息</h6>
    </div>
    <div class="card-body">
      <form method="post" id="purchase-order-form" novalidate novalidate>
        {{ form.csrf_token }}

        <!-- 隐藏区域选择，自动绑定用户学校 -->
        <div class="area-field-hidden" style="display: none;">
          {{ form.area_id(id="area_id") }}
          <label for="area_id" class="visually-hidden">区域ID</label>
        </div>

        <!-- 显示学校信息 -->
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">所属学校</label>
              <input type="text" class="form-control" value="{{ user_area.name }}" readonly>
              <small class="form-text text-muted">采购订单将自动绑定到您的学校</small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              {{ form.supplier_id.label(class="form-label") }}
              {{ form.supplier_id(class="form-control") }}
              {% if form.supplier_id.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.supplier_id.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">默认为自购，也可选择合作供应商</small>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              {{ form.order_date.label(class="form-label") }}
              {{ form.order_date(class="form-control", type="date") }}
              {% if form.order_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.order_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              {{ form.expected_delivery_date.label(class="form-label") }}
              {{ form.expected_delivery_date(class="form-control", type="date") }}
              {% if form.expected_delivery_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.expected_delivery_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              {{ form.batch_number.label(class="form-label") }}
              {{ form.batch_number(class="form-control", readonly=true) }}
              {% if form.batch_number.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.batch_number.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">系统自动生成</small>
            </div>
          </div>
        </div>

        <div class="mb-3">
          {{ form.notes.label(class="form-label") }}
          {{ form.notes(class="form-control", rows=3) }}
          {% if form.notes.errors %}
            <div class="invalid-feedback d-block">
              {% for error in form.notes.errors %}
                {{ error }}
              {% endfor %}
            </div>
          {% endif %}
        </div>

        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 fw-bold text-primary">采购明细</h6>
            <button type="button" class="btn btn-sm btn-primary" id="add-ingredient" data-original-onclick="openIngredientModal" data-action="add-ingredient">
              <i class="fas fa-plus" aria-hidden="true"></i> 添加食材
            </button>
          </div>
          <div class="card-body">
            <div id="ingredients-container">
              <!-- 默认不显示任何食材行，用户点击添加按钮后才显示 -->
              <div id="no-ingredients-message" class="text-center text-muted py-4">
                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                <p>暂无食材，请点击"添加食材"按钮开始添加</p>
              </div>
            </div>

            <div class="text-end mt-3">
              <h5>总金额: <span id="total-amount">0.00</span> 元</h5>
            </div>
          </div>
        </div>

        <div class="text-center">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> 创建采购订单
          </button>
          <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
            <i class="fas fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- 食材选择模态框 -->
<div class="modal fade" id="ingredientModal" tabindex="-1" role="dialog" aria-labelledby="ingredientModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ingredientModalLabel">选择食材</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="关闭食材选择对话框">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- 搜索栏 -->
        <div class="row mb-3">
          <div class="col-12">
            <label for="ingredient-search" class="visually-hidden">搜索食材</label>
            <input type="text" class="form-control" id="ingredient-search" placeholder="🔍 搜索食材名称..." aria-label="搜索食材名称">
          </div>
        </div>

        <!-- 食材选择界面 -->
        <div class="ingredient-selection-container">
          <!-- 控制栏 -->
          <div class="row mb-3">
            <div class="col-md-4">
              <label for="category-filter" class="visually-hidden">食材分类</label>
              <select class="form-control form-control-sm" id="category-filter" aria-label="选择食材分类">
                <option value="">加载中...</option>
              </select>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-primary btn-sm" id="load-ingredients" aria-label="加载食材数据" data-original-onclick="loadIngredients" data-action="load-ingredients">
                <i class="fas fa-sync" aria-hidden="true"></i> 加载食材
              </button>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-outline-success btn-sm select-all-ingredients" disabled aria-label="全选当前页食材" data-original-onclick="selectAllIngredients" data-action="select-all">
                <i class="fas fa-check-double" aria-hidden="true"></i> 全选当前页
              </button>
            </div>
          </div>

          <!-- 食材列表容器 -->
          <div id="ingredients-list-container">
            <div class="text-center text-muted py-4">
              <i class="fas fa-truck fa-2x mb-2"></i>
              <p>点击"加载食材"按钮选择供应商食材</p>
              <small>仅显示与您学校有合作关系的供应商提供的上架食材</small>
            </div>
          </div>

          <!-- 分页控件 -->
          <div id="pagination-container" class="d-flex justify-content-between align-items-center mt-3" style="display: none;">
            <div>
              <small class="text-muted" id="pagination-info"></small>
            </div>
            <div>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="prev-page" disabled aria-label="上一页" data-original-onclick="previousPage" data-action="prev-page">
                <i class="fas fa-chevron-left" aria-hidden="true"></i> 上一页
              </button>
              <span class="mx-2" id="page-info" aria-live="polite"></span>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="next-page" disabled aria-label="下一页" data-original-onclick="nextPage" data-action="next-page">
                下一页 <i class="fas fa-chevron-right" aria-hidden="true"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="add-selected-ingredients">添加选中的食材</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;
    let currentCategory = '';
    let ingredientsLoaded = false;
    let categoriesCache = null;
    let loadingRequest = null;

    // 初始化分类选择器
    initializeCategories();

    // 检查是否有来自消耗计划的预填充数据
    checkAndLoadPrefillData();

    // 计算总金额
    function calculateTotalAmount() {
      let total = 0;
      $('.ingredient-row').each(function() {
        const totalPrice = parseFloat($(this).find('input[name$="total_price"]').val()) || 0;
        total += totalPrice;
      });
      $('#total-amount').text(total.toFixed(2));
    }

    // 更新空食材提示的显示状态
    function updateNoIngredientsMessage() {
      if ($('.ingredient-row').length === 0) {
        $('#no-ingredients-message').show();
      } else {
        $('#no-ingredients-message').hide();
      }
    }

    // 计算行总价
    function calculateRowTotal(row) {
      const quantity = parseFloat($(row).find('.quantity-input').val()) || 0;
      const unitPrice = parseFloat($(row).find('.unit-price-input').val()) || 0;
      const totalPrice = quantity * unitPrice;
      $(row).find('input[name$="total_price"]').val(totalPrice.toFixed(2));
      calculateTotalAmount();
    }

    // 初始计算总金额和更新提示信息
    calculateTotalAmount();
    updateNoIngredientsMessage();

    // 监听数量和单价变化
    $(document).on('input', '.quantity-input, .unit-price-input', function() {
      calculateRowTotal($(this).closest('.ingredient-row'));
    });

    // 初始化分类选择器（简化版本）
    function initializeCategories() {
      const categorySelect = $('#category-filter');

      // 如果有缓存，直接使用
      if (categoriesCache) {
        let options = '';
        categoriesCache.forEach(function(category) {
          options += `<option value="${category}">${category}</option>`;
        });
        categorySelect.html(options);
        return;
      }

      // 使用简单的分类列表
      const defaultCategories = ['全部', '蔬菜', '肉类', '主食', '调料', '其他'];
      categoriesCache = defaultCategories;

      let options = '';
      defaultCategories.forEach(function(category) {
        options += `<option value="${category}">${category}</option>`;
      });
      categorySelect.html(options);
    }

    // 加载供应商食材数据（简化版本）
    function loadIngredients(page = 1, category = '') {
      const container = $('#ingredients-list-container');

      // 取消之前的请求
      if (loadingRequest) {
        loadingRequest.abort();
      }

      // 显示加载状态
      container.html(`
        <div class="text-center py-4">
          <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
          <p>正在加载供应商食材...</p>
          <small class="text-muted">仅显示合作供应商提供的上架食材</small>
        </div>
      `);

      // 使用简化的API调用
      loadingRequest = $.ajax({
        url: '/supplier-product/available-ingredients',
        method: 'GET',
        data: {
          page: page,
          per_page: 20,
          category: category === '全部' ? '' : category,
          search: $('#ingredient-search').val()
        },
        success: function(response) {
          loadingRequest = null;
          if (response.success && response.ingredients) {
            // 渲染食材列表
            renderIngredients(response.ingredients);

            // 更新分页信息（简化版本）
            updateSimplePagination(response.ingredients.length);

            // 启用全选按钮
            $('.select-all-ingredients').prop('disabled', false);

            ingredientsLoaded = true;
          } else {
            container.html(`
              <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i> 暂无可用的供应商食材
              </div>
            `);
          }
        },
        error: function(xhr) {
          loadingRequest = null;
          if (xhr.statusText !== 'abort') {
            // 如果API不存在，使用备用方案
            loadIngredientsBackup();
          }
        }
      });
    }

    // 备用食材加载方案
    function loadIngredientsBackup() {
      const container = $('#ingredients-list-container');

      // 使用现有的食材API作为备用
      $.ajax({
        url: '/ingredient/api',
        method: 'GET',
        success: function(data) {
          if (data && data.length > 0) {
            // 过滤和处理数据
            let filteredData = data.filter(item => item.status === 1);

            // 分类过滤
            const category = $('#category-filter').val();
            if (category && category !== '全部') {
              filteredData = filteredData.filter(item => item.category === category);
            }

            // 搜索过滤
            const searchTerm = $('#ingredient-search').val();
            if (searchTerm) {
              filteredData = filteredData.filter(item =>
                item.name.toLowerCase().includes(searchTerm.toLowerCase())
              );
            }

            // 转换数据格式
            const ingredients = filteredData.map(item => ({
              id: item.id,
              name: item.name,
              unit: item.unit || '',
              category: item.category || '未分类',
              has_supplier: true,
              source: '供应商',
              supplier_count: 1
            }));

            renderIngredients(ingredients);
            updateSimplePagination(ingredients.length);
            $('.select-all-ingredients').prop('disabled', false);
            ingredientsLoaded = true;
          } else {
            container.html(`
              <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i> 暂无可用食材
              </div>
            `);
          }
        },
        error: function() {
          container.html(`
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle"></i> 加载食材数据失败，请重试
            </div>
          `);
        }
      });
    }

    // 渲染食材列表（优化版本）
    function renderIngredients(ingredients) {
      const container = $('#ingredients-list-container');

      if (ingredients.length === 0) {
        container.html(`
          <div class="text-center text-muted py-4">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>没有找到符合条件的食材</p>
          </div>
        `);
        return;
      }

      // 使用DocumentFragment提高性能
      const fragment = document.createDocumentFragment();
      const gridDiv = document.createElement('div');
      gridDiv.className = 'ingredients-grid';

      // 添加说明信息
      if (ingredients.length > 0) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'col-12 mb-3';
        alertDiv.innerHTML = `
          <div class="alert alert-success py-2">
            <i class="fas fa-truck"></i>
            <strong>供应商食材：</strong>
            共找到 ${ingredients.length} 种由合作供应商提供的食材
          </div>
        `;
        fragment.appendChild(alertDiv);
      }

      // 批量创建食材元素
      ingredients.forEach(function(ingredient, index) {
        const ingredientId = ingredient.id || `temp-${index}`;
        const supplierCountText = ingredient.supplier_count > 1 ? ` (${ingredient.supplier_count}个供应商)` : '';

        const itemDiv = document.createElement('div');
        itemDiv.className = 'ingredient-item supplier-item';
        itemDiv.setAttribute('data-id', ingredient.id || '');
        itemDiv.setAttribute('data-name', ingredient.name || '');
        itemDiv.setAttribute('data-unit', ingredient.unit || '');
        itemDiv.setAttribute('data-category', ingredient.category || '');
        itemDiv.setAttribute('data-has-supplier', 'true');

        itemDiv.innerHTML = `
          <div class="form-check">
            <input type="checkbox" class="custom-control-input ingredient-checkbox" id="ingredient-${ingredientId}" value="${ingredient.id || ''}">
            <label class="form-check-label" for="ingredient-${ingredientId}">
              <div class="ingredient-info">
                <strong>${ingredient.name || '未知食材'}</strong>
                <small class="text-muted d-block">${ingredient.unit || ''}</small>
                <small class="text-success d-block">
                  <i class="fas fa-truck"></i> 供应商提供${supplierCountText}
                </small>
              </div>
            </label>
          </div>
        `;

        gridDiv.appendChild(itemDiv);
      });

      fragment.appendChild(gridDiv);

      // 一次性更新DOM
      container.empty().append(fragment);
    }

    // 更新分页信息
    function updatePagination(pagination) {
      const startIndex = (pagination.page - 1) * pagination.per_page + 1;
      const endIndex = Math.min(pagination.page * pagination.per_page, pagination.total);

      $('#pagination-info').html(`
        显示第 ${startIndex} - ${endIndex} 条，共 ${pagination.total} 条供应商食材
        <small class="text-muted d-block">
          <i class="fas fa-truck"></i> 仅显示合作供应商提供的食材
        </small>
      `);
      $('#page-info').text(`${pagination.page} / ${pagination.pages}`);

      $('#prev-page').prop('disabled', !pagination.has_prev);
      $('#next-page').prop('disabled', !pagination.has_next);

      $('#pagination-container').show();
    }

    // 简化的分页更新
    function updateSimplePagination(totalCount) {
      $('#pagination-info').html(`
        共找到 ${totalCount} 种供应商食材
        <small class="text-muted d-block">
          <i class="fas fa-truck"></i> 仅显示合作供应商提供的食材
        </small>
      `);
      $('#page-info').text('1 / 1');
      $('#prev-page').prop('disabled', true);
      $('#next-page').prop('disabled', true);
      $('#pagination-container').show();
    }

    // 添加食材按钮
    $('#add-ingredient').click(function() {
      $('#ingredientModal').modal('show');

      // 如果还没有加载过食材，自动加载
      if (!ingredientsLoaded) {
        loadIngredients();
      }
    });

    // 移除食材按钮
    $(document).on('click', '.remove-ingredient', function() {
      $(this).closest('.ingredient-row').remove();
      calculateTotalAmount();
      updateNoIngredientsMessage();
    });

    // 事件监听器
    $('#load-ingredients').click(function() {
      currentCategory = $('#category-filter').val();
      loadIngredients(1, currentCategory);
    });

    $('#category-filter').change(function() {
      if (ingredientsLoaded) {
        currentCategory = $(this).val();
        loadIngredients(1, currentCategory);
      }
    });

    $('#prev-page').click(function() {
      if (currentPage > 1) {
        loadIngredients(currentPage - 1, currentCategory);
      }
    });

    $('#next-page').click(function() {
      if (currentPage < totalPages) {
        loadIngredients(currentPage + 1, currentCategory);
      }
    });

    // 食材搜索（实时搜索）
    let searchTimeout;
    $('#ingredient-search').on('keyup', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(function() {
        if (ingredientsLoaded) {
          loadIngredients(1, currentCategory);
        }
      }, 500);
    });

    // 全选当前页食材
    $(document).on('click', '.select-all-ingredients', function() {
      const checkboxes = $('#ingredients-list-container .ingredient-checkbox');
      const allChecked = checkboxes.filter(':checked').length === checkboxes.length;

      // 如果全部选中则取消全选，否则全选
      checkboxes.prop('checked', !allChecked);

      // 更新按钮文字
      $(this).html(allChecked ? '<i class="fas fa-check-double"></i> 全选当前页' : '<i class="fas fa-times"></i> 取消全选');
    });


    // 点击食材项选择/取消选择（排除checkbox和label点击）
    $(document).on('click', '.ingredient-item', function(e) {
      // 如果点击的是checkbox、label或者label内的元素，不处理
      if ($(e.target).is('input[type="checkbox"]') ||
          $(e.target).is('label') ||
          $(e.target).closest('label').length > 0) {
        return;
      }

      // 点击其他区域时切换checkbox状态
      const checkbox = $(this).find('.ingredient-checkbox');
      checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });

    // 确保checkbox点击事件正常工作
    $(document).on('change', '.ingredient-checkbox', function(e) {
      // 阻止事件冒泡，避免触发父元素的点击事件
      e.stopPropagation();
    });

    // 注释掉不存在的标签页事件
    // $('a[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
    //   $('#ingredient-search').val('');
    //   $('.ingredient-item').show();
    // });

    // 添加选中的食材
    $('#add-selected-ingredients').click(function() {
      const selectedIngredients = [];

      $('.ingredient-item').each(function() {
        if ($(this).find('.ingredient-checkbox').prop('checked')) {
          selectedIngredients.push({
            id: $(this).data('id'),
            name: $(this).data('name'),
            unit: $(this).data('unit')
          });
        }
      });

      if (selectedIngredients.length > 0) {
        for (const ingredient of selectedIngredients) {
          addIngredientRow(ingredient);
        }
        $('#ingredientModal').modal('hide');
        // 重置复选框和按钮文字
        $('.ingredient-checkbox').prop('checked', false);
        $('.select-all-ingredients').html('<i class="fas fa-check-double"></i> 全选当前页');
        updateNoIngredientsMessage();
      } else {
        alert('请至少选择一种食材');
      }
    });

    // 获取供应商选项
    function getSupplierOptions() {
      let options = '';
      {% for value, label in form.supplier_id.choices %}
        {% if value and value != 0 %}
          options += '<option value="{{ value }}">{{ label }}</option>';
        {% endif %}
      {% endfor %}
      return options;
    }

    // 加载食材的供应商列表
    function loadIngredientSuppliers(ingredientId, row) {
      const supplierSelect = row.find('.supplier-select');
      const supplierHint = row.find('.supplier-hint');

      $.ajax({
        url: `/purchase-order/ingredient-suppliers/${ingredientId}`,
        method: 'GET',
        success: function(response) {
          if (response.success && response.suppliers.length > 0) {
            let options = '<option value="">自购</option>';
            response.suppliers.forEach(function(supplier) {
              options += `<option value="${supplier.id}" data-price="${supplier.price}" data-product-id="${supplier.product_id}">${supplier.name} (¥${supplier.price}/${supplier.unit})</option>`;
            });
            supplierSelect.html(options);
            supplierHint.text(`找到 ${response.suppliers.length} 个供应商`);
          } else {
            supplierSelect.html('<option value="">自购</option>');
            supplierHint.text('暂无合作供应商，将使用自购方式');
          }
        },
        error: function() {
          supplierSelect.html('<option value="">自购</option>');
          supplierHint.text('加载供应商失败，将使用自购方式');
        }
      });
    }

    // 供应商选择变化时自动更新价格
    $(document).on('change', '.supplier-select', function() {
      const row = $(this).closest('.ingredient-row');
      const selectedOption = $(this).find('option:selected');
      const price = selectedOption.data('price');
      const productId = selectedOption.data('product-id');
      const priceInput = row.find('.unit-price-input');
      const priceHint = row.find('.price-hint');
      const productIdInput = row.find('input[name$="-product_id"]');

      if (price && price > 0) {
        priceInput.val(price);
        priceHint.text(`供应商价格: ¥${price}`);
        productIdInput.val(productId || '');

        // 重新计算总价
        calculateRowTotal(row);

        // 显示价格更新提示
        priceHint.addClass('text-success').removeClass('text-muted');
        setTimeout(function() {
          priceHint.removeClass('text-success').addClass('text-muted');
        }, 2000);
      } else {
        priceInput.val(0);
        priceHint.text('请手动输入价格');
        productIdInput.val('');
      }
    });

    // 添加食材行
    function addIngredientRow(ingredient) {
      const index = $('.ingredient-row').length;
      const template = `
        <div class="ingredient-row" data-ingredient-id="${ingredient.id}">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label" for="ingredient-name-${index}">食材名称</label>
              <input type="text" class="form-control" id="ingredient-name-${index}" name="items-${index}-ingredient_name" value="${ingredient.name}" readonly aria-label="食材名称: ${ingredient.name}">
              <input type="hidden" name="items-${index}-ingredient_id" value="${ingredient.id}">
            </div>
            <div class="col-md-2">
              <label class="form-label" for="quantity-${index}">数量</label>
              <input type="number" class="form-control quantity-input" id="quantity-${index}" name="items-${index}-quantity" value="1" min="0.01" step="0.01" aria-label="食材数量">
            </div>
            <div class="col-md-1">
              <label class="form-label" for="unit-${index}">单位</label>
              <input type="text" class="form-control" id="unit-${index}" name="items-${index}-unit" value="${ingredient.unit}" readonly aria-label="食材单位">
            </div>
            <div class="col-md-2">
              <label class="form-label" for="unit-price-${index}">单价</label>
              <input type="number" class="form-control unit-price-input" id="unit-price-${index}" name="items-${index}-unit_price" value="0" min="0" step="0.01" aria-label="食材单价">
              <small class="text-muted price-hint"></small>
            </div>
            <div class="col-md-2">
              <label class="form-label" for="total-price-${index}">总价</label>
              <input type="text" class="form-control" id="total-price-${index}" name="items-${index}-total_price" value="0.00" readonly aria-label="食材总价">
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button type="button" class="btn btn-danger w-100 remove-ingredient" data-original-onclick="removeIngredientRow" data-delete-code="ingredient-${index}">
                <i class="fas fa-trash" aria-hidden="true"></i> 移除
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="form-label">供应商</label>
              <select class="form-control supplier-select" name="items-${index}-supplier_id" data-ingredient-id="${ingredient.id}">
                <option value="">正在加载供应商...</option>
              </select>
              <small class="text-muted supplier-hint">选择供应商后自动获取价格</small>
            </div>
            <div class="col-md-6">
              <label class="form-label">备注</label>
              <input type="text" class="form-control" name="items-${index}-notes">
            </div>
          </div>
          <input type="hidden" name="items-${index}-product_id" value="">
          <hr class="my-3">
        </div>
      `;

      $('#ingredients-container').append(template);
      const newRow = $('.ingredient-row').last();

      // 加载该食材的供应商
      loadIngredientSuppliers(ingredient.id, newRow);

      calculateRowTotal(newRow);
    }

    // 表单提交前验证
    $('#purchase-order-form').submit(function(e) {
      if ($('.ingredient-row').length === 0) {
        e.preventDefault();
        alert('请至少添加一种食材');
        return false;
      }

      // 确保表单有正确的字段名格式
      $('.ingredient-row').each(function(index) {
        $(this).find('input, select').each(function() {
          const name = $(this).attr('name');
          if (name && name.includes('items-')) {
            const newName = name.replace(/items-\d+-/, `items-${index}-`);
            $(this).attr('name', newName);
          }
        });
      });

      // 检查数量和单价
      let valid = true;
      $('.ingredient-row').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const unitPrice = parseFloat($(this).find('.unit-price-input').val()) || 0;

        if (quantity <= 0) {
          valid = false;
          $(this).find('.quantity-input').addClass('is-invalid');
        } else {
          $(this).find('.quantity-input').removeClass('is-invalid');
        }

        if (unitPrice < 0) {
          valid = false;
          $(this).find('.unit-price-input').addClass('is-invalid');
        } else {
          $(this).find('.unit-price-input').removeClass('is-invalid');
        }
      });

      if (!valid) {
        e.preventDefault();
        alert('请检查数量和单价');
        return false;
      }
    });

    // 检查并加载预填充数据
    function checkAndLoadPrefillData() {
      // 检查URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const fromParam = urlParams.get('from');

      if (fromParam === 'consumption-plan') {
        // 从sessionStorage获取数据
        const purchaseDataStr = sessionStorage.getItem('purchase_order_data');
        if (purchaseDataStr) {
          try {
            const purchaseData = JSON.parse(purchaseDataStr);
            console.log('加载预填充数据:', purchaseData);

            // 显示来源信息
            showPrefillInfo(purchaseData);

            // 预填充食材
            if (purchaseData.missing_ingredients && purchaseData.missing_ingredients.length > 0) {
              prefillIngredients(purchaseData.missing_ingredients);
            }

            // 清除sessionStorage中的数据
            sessionStorage.removeItem('purchase_order_data');
          } catch (e) {
            console.error('解析预填充数据失败:', e);
          }
        }
      }
    }

    // 显示预填充信息
    function showPrefillInfo(purchaseData) {
      const infoHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
          <h6><i class="fas fa-info-circle"></i> 来自消耗计划的采购需求</h6>
          <p class="mb-1">
            <strong>消耗日期:</strong> ${purchaseData.consumption_date}<br>
            <strong>餐次:</strong> ${purchaseData.meal_types.join(', ')}<br>
            <strong>缺少食材:</strong> ${purchaseData.missing_ingredients.length} 种
          </p>
          <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      `;

      // 在表单前插入信息
      $('.card-body').first().prepend(infoHtml);
    }

    // 预填充食材
    function prefillIngredients(missingIngredients) {
      // 隐藏"暂无食材"提示
      $('#no-ingredients-message').hide();

      console.log('开始预填充食材:', missingIngredients);

      missingIngredients.forEach(function(ingredient, index) {
        // 直接使用常见的标准单位
        ingredient.standard_unit = getStandardUnit(ingredient.name);
        addPrefillIngredientRow(ingredient);
      });
    }

    // 获取食材的标准单位
    function getStandardUnit(ingredientName) {
      // 常见食材的标准单位映射
      const unitMap = {
        '土豆': '公斤', '胡萝卜': '公斤', '白菜': '公斤', '萝卜': '公斤',
        '猪肉': '公斤', '牛肉': '公斤', '鸡肉': '公斤', '鱼肉': '公斤',
        '大米': '公斤', '面粉': '公斤', '油': '升', '盐': '公斤',
        '鸡蛋': '个', '牛奶': '升', '豆腐': '块', '面条': '公斤'
      };

      // 根据食材名称匹配标准单位
      for (let key in unitMap) {
        if (ingredientName.includes(key)) {
          return unitMap[key];
        }
      }

      // 默认返回公斤
      return '公斤';
    }

    // 添加预填充食材行（专用于从消耗计划预填充）
    function addPrefillIngredientRow(ingredient) {
      console.log('添加预填充食材行:', ingredient);

      const index = $('.ingredient-row').length;
      const template = `
        <div class="ingredient-row" data-ingredient-name="${ingredient.name}">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">食材名称</label>
              <input type="text" class="form-control" name="items-${index}-ingredient_name" value="${ingredient.name}" required>
              <input type="hidden" name="items-${index}-ingredient_id" value="">
              <input type="hidden" name="items-${index}-product_id" value="">
              <small class="text-muted">来自消耗计划分析</small>
            </div>
            <div class="col-md-2">
              <label class="form-label">数量</label>
              <input type="number" class="form-control quantity-input" name="items-${index}-quantity" value="${ingredient.shortage_quantity || 1}" min="0.01" step="0.01" required>
            </div>
            <div class="col-md-1">
              <label class="form-label">单位</label>
              <input type="text" class="form-control" name="items-${index}-unit" value="${ingredient.standard_unit || ingredient.unit || ''}" required>
              <small class="text-muted">标准单位</small>
            </div>
            <div class="col-md-2">
              <label class="form-label">单价</label>
              <input type="number" class="form-control unit-price-input" name="items-${index}-unit_price" value="0" min="0" step="0.01" required>
            </div>
            <div class="col-md-2">
              <label class="form-label">总价</label>
              <input type="number" class="form-control" name="items-${index}-total_price" value="0" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">操作</label>
              <button type="button" class="btn btn-danger btn-sm remove-ingredient" data-original-onclick="removeIngredientRow" data-delete-code="prefill-ingredient-${index}">
                <i class="fas fa-trash" aria-hidden="true"></i> 移除
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="form-label">供应商</label>
              <select class="form-control supplier-select" name="items-${index}-supplier_id" data-ingredient-id="">
                <option value="">正在加载供应商...</option>
              </select>
              <small class="text-muted supplier-hint">选择供应商后自动获取价格</small>
            </div>
            <div class="col-md-6">
              <label class="form-label">备注</label>
              <input type="text" class="form-control" name="items-${index}-notes" value="来自消耗计划：${ingredient.details ? ingredient.details.map(d => d.meal_type + '(' + d.recipe_name + ')').join(', ') : ''}">
            </div>
          </div>
          <input type="hidden" name="items-${index}-product_id" value="">
          <hr class="my-3">
        </div>
      `;

      $('#ingredients-container').append(template);

      // 计算总价
      const $newRow = $('.ingredient-row').last();

      // 根据食材名称查找ID，然后加载供应商
      $.ajax({
        url: '/purchase-order/available-ingredients',
        method: 'GET',
        data: {
          search: ingredient.name,
          per_page: 50
        },
        success: function(response) {
          if (response.success && response.data.ingredients.length > 0) {
            const foundIngredient = response.data.ingredients.find(item => item.name === ingredient.name);
            if (foundIngredient) {
              $newRow.find('input[name$="-ingredient_id"]').val(foundIngredient.id);
              $newRow.attr('data-ingredient-id', foundIngredient.id);
              $newRow.find('.supplier-select').attr('data-ingredient-id', foundIngredient.id);

              // 加载该食材的供应商
              loadIngredientSuppliers(foundIngredient.id, $newRow);

              // 显示食材来源提示
              if (foundIngredient.has_supplier) {
                $newRow.find('.supplier-hint').text('该食材有供应商提供，建议选择供应商');
              } else {
                $newRow.find('.supplier-hint').text('该食材仅支持自购方式');
              }
            } else {
              // 如果找不到食材，显示自购选项
              $newRow.find('.supplier-select').html('<option value="">自购</option>');
              $newRow.find('.supplier-hint').text('未找到食材信息，使用自购方式');
            }
          } else {
            // 如果找不到食材，显示自购选项
            $newRow.find('.supplier-select').html('<option value="">自购</option>');
            $newRow.find('.supplier-hint').text('未找到可用食材，使用自购方式');
          }
        },
        error: function() {
          $newRow.find('.supplier-select').html('<option value="">自购</option>');
          $newRow.find('.supplier-hint').text('加载失败，使用自购方式');
        }
      });

      calculateRowTotal($newRow);

      console.log('预填充食材行添加完成');
    }

  });
</script>
{% endblock %}
