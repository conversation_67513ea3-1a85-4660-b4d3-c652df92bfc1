<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ project_name }}{% endblock %}</title>
    
    <!-- 动态资源加载 -->
    {% if resources.use_cdn %}
        <!-- CDN 资源 -->
        <script nonce="{{ csp_nonce }}" src="{{ resources.css.tailwind }}"></script>
        <link href="{{ resources.css.fontawesome }}" rel="stylesheet">
        {% if resources.css.bootstrap %}
        <link href="{{ resources.css.bootstrap }}" rel="stylesheet">
        {% endif %}
        {% if resources.fonts.google_fonts %}
        <link href="{{ resources.fonts.google_fonts }}" rel="stylesheet">
        {% endif %}
        {% if resources.fonts.chinese_fonts %}
        <link href="{{ resources.fonts.chinese_fonts }}" rel="stylesheet">
        {% endif %}
    {% else %}
        <!-- 本地资源 -->
        <link href="{{ url_for('static', filename='css/tailwind-custom.css') }}" rel="stylesheet">
        <link href="{{ url_for('static', filename='css/all.min.css') }}" rel="stylesheet">
        {% if resources.css.bootstrap %}
        <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
        {% endif %}
        <link href="{{ url_for('static', filename='fonts/inter.css') }}" rel="stylesheet">
        <link href="{{ url_for('static', filename='fonts/noto-sans-sc.css') }}" rel="stylesheet">
    {% endif %}
    
    <!-- Tailwind CSS 配置 -->
    {% if resources.use_cdn %}
    <script nonce="{{ csp_nonce }}">
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#36CBCB',
                        accent: '#722ED1',
                        dark: '#1D2129',
                        light: '#F7F8FA',
                        'primary-light': '#E8F3FF',
                        'primary-dark': '#0D47A1',
                        'neon-blue': '#00BFFF',
                        'neon-purple': '#9D4EDD',
                        'neon-green': '#00FF9D',
                        'dark-blue': '#0B0E2F',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                        code: ['JetBrains Mono', 'monospace'],
                    },
                },
            }
        }
    </script>
    {% endif %}
    
    <!-- 自定义样式 -->
    {% block extra_css %}{% endblock %}
    
    <style nonce="{{ csp_nonce }}" type="{% if resources.use_cdn %}text/tailwindcss{% else %}text/css{% endif %}">
        {% if resources.use_cdn %}
        @layer utilities {
        {% endif %}
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .card-hover {
                {% if resources.use_cdn %}
                @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
                {% else %}
                transition: all 0.3s ease;
                {% endif %}
            }
            {% if not resources.use_cdn %}
            .card-hover:hover {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                transform: translateY(-0.25rem);
            }
            {% endif %}
            .section-padding {
                {% if resources.use_cdn %}
                @apply py-16 md:py-24;
                {% else %}
                padding-top: 4rem;
                padding-bottom: 4rem;
                {% endif %}
            }
            {% if not resources.use_cdn %}
            @d-flex (min-width: 768px) {
                .section-padding {
                    padding-top: 6rem;
                    padding-bottom: 6rem;
                }
            }
            {% endif %}
            .bg-gradient-primary {
                {% if resources.use_cdn %}
                @apply bg-gradient-to-r from-primary to-primary-dark;
                {% else %}
                background: linear-gradient(to right, #165DFF, #0D47A1);
                {% endif %}
            }
            .animate-float {
                animation: float 6s ease-in-out infinite;
            }
            .neon-glow {
                box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
            }
            .neon-text {
                text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
            }
            .data-pulse {
                animation: pulse 2s infinite;
            }
            @keyframes float {
                0% { transform: translateY(0px); }
                50% { transform: translateY(-20px); }
                100% { transform: translateY(0px); }
            }
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.05); opacity: 0.8; }
                100% { transform: scale(1); opacity: 1; }
            }
            .bg-grid {
                background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                                  linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
                background-size: 20px 20px;
            }
            .clip-path-slant {
                clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
            }
        {% if resources.use_cdn %}
        }
        {% endif %}
    </style>
</head>

<body class="{% block body_class %}font-inter bg-gray-50 text-gray-900{% endblock %}">
    {% block content %}{% endblock %}
    
    <!-- JavaScript 资源 -->
    {% if resources.use_cdn %}
        <!-- CDN JavaScript -->
        {% if resources.js.jquery %}
        <script nonce="{{ csp_nonce }}" src="{{ resources.js.jquery }}"></script>
        {% endif %}
        {% if resources.js.bootstrap %}
        <script nonce="{{ csp_nonce }}" src="{{ resources.js.bootstrap }}"></script>
        {% endif %}
        {% if resources.js.chartjs %}
        <script nonce="{{ csp_nonce }}" src="{{ resources.js.chartjs }}"></script>
        {% endif %}
        {% if resources.js.alpinejs %}
        <script nonce="{{ csp_nonce }}" defer src="{{ resources.js.alpinejs }}"></script>
        {% endif %}
        {% if resources.js.axios %}
        <script nonce="{{ csp_nonce }}" src="{{ resources.js.axios }}"></script>
        {% endif %}
    {% else %}
        <!-- 本地 JavaScript -->
        {% if resources.js.jquery %}
        <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/jquery-3.7.1.min.js') }}"></script>
        {% endif %}
        {% if resources.js.bootstrap %}
        <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
        {% endif %}
        {% if resources.js.chartjs %}
        <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>
        {% endif %}
        {% if resources.js.alpinejs %}
        <script nonce="{{ csp_nonce }}" defer src="{{ url_for('static', filename='js/alpine.min.js') }}"></script>
        {% endif %}
        {% if resources.js.axios %}
        <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/axios.min.js') }}"></script>
        {% endif %}
    {% endif %}
    
    <!-- 自定义 JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- 资源模式指示器（仅在开发模式下显示） -->
    {% if config.DEBUG %}
    <div class="fixed bottom-4 end-4 z-50">
        <div class="bg-{{ 'green' if resources.use_cdn else 'blue' }}-500 text-white px-3 py-1 rounded-full text-xs">
            {{ 'CDN' if resources.use_cdn else 'Local' }} Mode
        </div>
    </div>
    {% endif %}
</body>
</html>
