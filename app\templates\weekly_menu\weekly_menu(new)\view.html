{% extends 'base.html' %}

{% block title %}周菜单详情 - {{ super() }}{% endblock %}

{% block content %}
{% if weekly_menu.status == '计划中' %}
<div class="alert alert-success alert-dismissible fade show" role="alert">
  <strong><i class="fas fa-check-circle"></i> 菜单计划已保存</strong>
  <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
{% endif %}

<div class="container-fluid">
  <div class="row mb-4 align-items-center">
    <div class="col-md-7">
      <h2 class="mb-1">{{ weekly_menu.area.name }}周菜单详情</h2>
      <p class="text-muted mb-0">{{ weekly_menu.week_start|format_datetime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end|format_datetime('%Y-%m-%d') }}</p>
    </div>
    <div class="col-md-5">
      <div class="btn-toolbar float-end" role="toolbar">
        <div class="btn-group me-2" role="group">
          <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
          </a>
        </div>

        <div class="btn-group me-2" role="group">
          {% if weekly_menu.status == '计划中' %}
          <a href="{{ url_for('weekly_menu.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start|format_datetime('%Y-%m-%d')) }}" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i> 编辑菜单
          </a>
          {% endif %}
          <a href="{{ url_for('weekly_menu.print_menu', id=weekly_menu.id, t=now()|int) }}" class="btn btn-outline-info" target="_blank">
            <i class="fas fa-print"></i> 打印菜单
          </a>
          <!-- 复制到下周功能已移除 -->
          <!-- <a href="{{ url_for('weekly_menu.copy_to_next_week', id=weekly_menu.id) }}" class="btn btn-outline-success">
            <i class="fas fa-copy"></i> 复制到下周
          </a> -->
        </div>

        <div class="btn-group" role="group">
          {% if weekly_menu.status == '计划中' %}
          <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#publishModal">
            <i class="fas fa-check-circle"></i> 发布菜单
          </button>
          {% endif %}
          <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
            <i class="fas fa-trash"></i> 删除
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 菜单基本信息 -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>基本信息</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">学校</div>
            <div class="info-value">{{ weekly_menu.area.name }}</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">周开始日期</div>
            <div class="info-value">{{ weekly_menu.week_start|format_datetime('%Y-%m-%d') }}</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">周结束日期</div>
            <div class="info-value">{{ weekly_menu.week_end|format_datetime('%Y-%m-%d') }}</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">状态</div>
            <div class="info-value">
              {% if weekly_menu.status == '计划中' %}
              <span class="status-badge status-planning">计划中</span>
              {% elif weekly_menu.status == '已发布' %}
              <span class="status-badge status-published">已发布</span>
              {% else %}
              <span class="status-badge status-other">{{ weekly_menu.status }}</span>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">创建人</div>
            <div class="info-value">{{ weekly_menu.creator.real_name or weekly_menu.creator.username }}</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">创建时间</div>
            <div class="info-value">{{ weekly_menu.created_at|format_datetime }}</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="info-item">
            <div class="info-label">最后更新</div>
            <div class="info-value">{{ weekly_menu.updated_at|format_datetime }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 周菜单表格 -->
  <div class="card">
    <div class="card-header bg-primary text-white">
      <h5 class="mb-0"><i class="fas fa-utensils me-2"></i>周菜单安排</h5>
    </div>
    <div class="card-body p-0">
      <table class="table table-hover menu-table mb-0">
        <thead class="thead-light">
          <tr>
            <th class="w-15" class="text-center">日期</th>
            <th class="w-30" class="text-center">早餐</th>
            <th class="w-30" class="text-center">午餐</th>
            <th class="w-30" class="text-center">晚餐</th>
          </tr>
        </thead>
        <tbody>
          {% for date_str, day_data in week_data.items() %}
          <tr>
            <td class="text-center align-middle date-cell">
              <div class="weekday-label">{{ day_data.weekday }}</div>
              <div class="date-label">{{ date_str }}</div>
            </td>
            {% for meal_type in ['早餐', '午餐', '晚餐'] %}
            <td class="meal-cell">
              {% if day_data.meals[meal_type] %}
                <div class="recipe-container">
                {% for recipe in day_data.meals[meal_type] %}
                  <span class="recipe-badge">{{ recipe.name }}</span>
                {% endfor %}
                </div>
              {% else %}
                <div class="text-muted text-center empty-meal">未安排</div>
              {% endif %}
            </td>
            {% endfor %}
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- 发布确认对话框 -->
<div class="modal fade" id="publishModal" tabindex="-1" role="dialog" aria-labelledby="publishModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title" id="publishModalLabel"><i class="fas fa-check-circle me-2"></i>确认发布</h5>
        <button type="button" class="close text-white" data-bs-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body py-4">
        <div class="text-center mb-4">
          <i class="fas fa-clipboard-check text-success" style="font-size: 3rem;"></i>
        </div>
        <p class="text-center">您确定要发布这个周菜单计划吗？</p>
        <p class="text-center text-muted small">发布后将不能再进行编辑。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
        <form action="{{ url_for('weekly_menu.publish', id=weekly_menu.id) }}" method="post" novalidate novalidate>
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-success px-4">确认发布</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="deleteModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>确认删除</h5>
        <button type="button" class="close text-white" data-bs-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body py-4">
        <div class="text-center mb-4">
          <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
        </div>
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-circle me-2"></i><strong>警告：</strong>此操作不可逆！
        </div>
        <p class="text-center">您确定要删除这个周菜单计划吗？</p>
        <p class="text-center text-muted small">删除后将无法恢复。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
        <form action="{{ url_for('weekly_menu.delete_menu', id=weekly_menu.id) }}" method="post" novalidate novalidate>
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-danger px-4">确认删除</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  /* 菜单表格样式 */
  .menu-table {
    border-collapse: separate;
    border-spacing: 0;
  }

  .menu-table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
  }

  .date-cell {
    background-color: #f8f9fa;
    border-end: 1px solid #dee2e6;
  }

  .weekday-label {
    font-weight: 600;
    font-size: 1.1em;
    color: #495057;
  }

  .date-label {
    font-size: 0.9em;
    color: #6c757d;
  }

  .meal-cell {
    padding: 12px 15px;
    vertical-align: middle;
  }

  .recipe-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .recipe-badge {
    display: inline-block;
    background-color: #17a2b8;
    color: white;
    font-size: 0.9em;
    padding: 5px 10px;
    border-radius: 4px;
  }

  .empty-meal {
    padding: 8px;
    font-style: italic;
  }

  /* 按钮样式优化 */
  .btn-toolbar .btn-group {
    margin-bottom: 5px;
  }

  /* 提示条样式 */
  .alert-success {
    border-start: 4px solid #28a745;
  }

  /* 卡片样式 */
  .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 25px;
  }

  .card-header {
    border-bottom: 0;
    padding: 15px 20px;
  }

  /* 信息项样式 */
  .info-item {
    display: flex;
    flex-direction: column;
  }

  .info-label {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
  }

  .info-value {
    font-size: 1em;
    color: #212529;
    font-weight: 500;
  }

  /* 状态标签样式 */
  .status-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
  }

  .status-planning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
  }

  .status-published {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .status-other {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
  }
</style>
{% endblock %}
