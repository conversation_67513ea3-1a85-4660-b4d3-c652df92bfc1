{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplier_product.edit', id=product.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('supplier_product.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">产品名称</th>
                                    <td>{{ product.product_name or product.ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>供应商</th>
                                    <td>{{ product.supplier.name }}</td>
                                </tr>
                                <tr>
                                    <th>食材</th>
                                    <td>{{ product.ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>产品编码</th>
                                    <td>{{ product.product_code or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>型号</th>
                                    <td>{{ product.model_number or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>规格</th>
                                    <td>{{ product.specification or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>单价</th>
                                    <td>¥{{ product.price }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">质量认证</th>
                                    <td>{{ product.quality_cert }}</td>
                                </tr>
                                <tr>
                                    <th>质量标准</th>
                                    <td>{{ product.quality_standard or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>供货周期</th>
                                    <td>{{ product.lead_time or '-' }} 天</td>
                                </tr>
                                <tr>
                                    <th>最小订购量</th>
                                    <td>{{ product.min_order_quantity or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>审核状态</th>
                                    <td>
                                        {% if product.shelf_status == 0 %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif product.shelf_status == 1 %}
                                        <span class="badge badge-success">已审核</span>
                                        {% else %}
                                        <span class="badge badge-danger">已拒绝</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>上架状态</th>
                                    <td>
                                        {% if product.is_available == 1 %}
                                        <span class="badge badge-success">已上架</span>
                                        {% else %}
                                        <span class="badge badge-secondary">未上架</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>上架时间</th>
                                    <td>{{  product.shelf_time|format_datetime('%Y-%m-%d %H:%M:%S') if product.shelf_time else '-'  }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">产品描述</h5>
                                </div>
                                <div class="card-body">
                                    {% if product.description %}
                                    <p>{{ product.description }}</p>
                                    {% else %}
                                    <p class="text-muted">暂无产品描述</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if product.product_image %}
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">产品图片</h5>
                                </div>
                                <div class="card-body text-center">
                                    <img src="{{ url_for('static', filename=product.product_image) }}" alt="产品图片" class="img-fluid" style="max-height: 300px;">
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">规格参数</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>参数名称</th>
                                                        <th>参数值</th>
                                                        <th>单位</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for param in spec_parameters %}
                                                    <tr>
                                                        <td>{{ param.param_name }}</td>
                                                        <td>{{ param.param_value }}</td>
                                                        <td>{{ param.param_unit or '-' }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-danger delete-param-btn" data-id="{{ param.id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    {% else %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">暂无规格参数</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="card-title">添加参数</h6>
                                                </div>
                                                <div class="card-body">
                                                    <form method="POST" action="{{ url_for('supplier_product.add_parameter') }}">
        {{ csrf_token() }}
                                                        {{ param_form.hidden_tag() }}
                                                        <div class="mb-3">
                                                            {{ param_form.param_name.label }}
                                                            {{ param_form.param_name(class="form-control") }}
                                                            {% for error in param_form.param_name.errors %}
                                                            <small class="text-danger">{{ error }}</small>
                                                            {% endfor %}
                                                        </div>
                                                        <div class="mb-3">
                                                            {{ param_form.param_value.label }}
                                                            {{ param_form.param_value(class="form-control") }}
                                                            {% for error in param_form.param_value.errors %}
                                                            <small class="text-danger">{{ error }}</small>
                                                            {% endfor %}
                                                        </div>
                                                        <div class="mb-3">
                                                            {{ param_form.param_unit.label }}
                                                            {{ param_form.param_unit(class="form-control") }}
                                                            {% for error in param_form.param_unit.errors %}
                                                            <small class="text-danger">{{ error }}</small>
                                                            {% endfor %}
                                                        </div>
                                                        <div class="mb-3">
                                                            {{ param_form.submit(class="btn btn-primary w-100") }}
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">产品操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group">
                                        {% if product.shelf_status == 0 %}
                                        <button type="button" class="btn btn-success approve-btn" data-id="{{ product.id }}">
                                            <i class="fas fa-check"></i> 审核通过
                                        </button>
                                        <button type="button" class="btn btn-danger reject-btn" data-id="{{ product.id }}">
                                            <i class="fas fa-times"></i> 拒绝上架
                                        </button>
                                        {% elif product.shelf_status == 1 and product.is_available == 0 %}
                                        <button type="button" class="btn btn-success shelf-btn" data-id="{{ product.id }}">
                                            <i class="fas fa-arrow-up"></i> 上架产品
                                        </button>
                                        {% elif product.is_available == 1 %}
                                        <button type="button" class="btn btn-warning unshelf-btn" data-id="{{ product.id }}">
                                            <i class="fas fa-arrow-down"></i> 下架产品
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-danger delete-btn" data-id="{{ product.id }}">
                                            <i class="fas fa-trash"></i> 删除产品
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个产品吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除参数确认模态框 -->
<div class="modal fade" id="deleteParamModal" tabindex="-1" role="dialog" aria-labelledby="deleteParamModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteParamModalLabel">确认删除</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个规格参数吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteParam">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 拒绝原因模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">拒绝原因</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="mb-3">
                        <label for="rejectReason">请输入拒绝原因</label>
                        <textarea class="form-control" id="rejectReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmReject">确认拒绝</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除产品
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier_product.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.href = '{{ url_for("supplier_product.index") }}';
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });

        // 删除参数
        var deleteParamId = null;

        $('.delete-param-btn').click(function() {
            deleteParamId = $(this).data('id');
            $('#deleteParamModal').modal('show');
        });

        $('#confirmDeleteParam').click(function() {
            if (deleteParamId) {
                $.ajax({
                    url: '{{ url_for("supplier_product.delete_parameter", id=0) }}'.replace('0', deleteParamId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteParamModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteParamModal').modal('hide');
                    }
                });
            }
        });

        // 上架功能
        $('.shelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.shelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('上架失败，请稍后重试！');
                }
            });
        });

        // 下架功能
        $('.unshelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.unshelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('下架失败，请稍后重试！');
                }
            });
        });

        // 审核通过功能
        $('.approve-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.approve_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('审核失败，请稍后重试！');
                }
            });
        });

        // 拒绝功能
        var rejectId = null;

        $('.reject-btn').click(function() {
            rejectId = $(this).data('id');
            $('#rejectModal').modal('show');
        });

        $('#confirmReject').click(function() {
            if (rejectId) {
                var reason = $('#rejectReason').val();
                if (!reason) {
                    toastr.error('请输入拒绝原因！');
                    return;
                }

                $.ajax({
                    url: '{{ url_for("supplier_product.reject_product", id=0) }}'.replace('0', rejectId),
                    type: 'POST',
                    data: {
                        reason: reason
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#rejectModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('拒绝失败，请稍后重试！');
                        $('#rejectModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
