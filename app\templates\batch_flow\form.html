{% extends 'base.html' %}

{% block title %}添加批次流水{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">添加批次流水</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回批次详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">批次信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">批次号</th>
                                            <td>{{ batch.batch_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>食材</th>
                                            <td>{{ batch.ingredient.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>当前库存</th>
                                            <td>{{ batch.current_quantity }} {{ batch.unit }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>
                                                {% if batch.status == '正常' %}
                                                <span class="badge badge-success">正常</span>
                                                {% elif batch.status == '预警' %}
                                                <span class="badge badge-warning">预警</span>
                                                {% elif batch.status == '过期' %}
                                                <span class="badge badge-danger">过期</span>
                                                {% elif batch.status == '已用完' %}
                                                <span class="badge badge-secondary">已用完</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" novalidate novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="flow_type">流水类型 <span class="text-danger">*</span></label>
                                    <select name="flow_type" id="flow_type" class="form-control" required>
                                        <option value="">请选择流水类型</option>
                                        <option value="入库">入库</option>
                                        <option value="出库">出库</option>
                                        <option value="调整">调整</option>
                                        <option value="报损">报损</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="flow_direction">流向 <span class="text-danger">*</span></label>
                                    <select name="flow_direction" id="flow_direction" class="form-control" required>
                                        <option value="">请选择流向</option>
                                        <option value="增加">增加</option>
                                        <option value="减少">减少</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity">数量 <span class="text-danger">*</span></label>
                                    <input type="number" name="quantity" id="quantity" class="form-control" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unit">单位 <span class="text-danger">*</span></label>
                                    <input type="text" name="unit" id="unit" class="form-control" value="{{ batch.unit }}" readonly required>
                                    <small class="form-text text-muted">单位必须与批次单位一致</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="related_type">关联单据类型</label>
                                    <select name="related_type" id="related_type" class="form-control">
                                        <option value="">无</option>
                                        <option value="入库单">入库单</option>
                                        <option value="出库单">出库单</option>
                                        <option value="消耗计划">消耗计划</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="related_id">关联单据ID</label>
                                    <input type="number" name="related_id" id="related_id" class="form-control">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="remark">备注</label>
                            <textarea name="remark" id="remark" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="mb-3 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-default">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(function() {
        // 流水类型与流向联动
        $('#flow_type').change(function() {
            var flowType = $(this).val();
            var flowDirection = $('#flow_direction');

            if (flowType === '入库') {
                flowDirection.val('增加');
            } else if (flowType === '出库' || flowType === '报损') {
                flowDirection.val('减少');
            } else {
                flowDirection.val('');
            }
        });

        // 表单提交前验证
        $('form').submit(function(e) {
            var quantity = parseFloat($('#quantity').val());
            var flowDirection = $('#flow_direction').val();
            var currentStock = {{ batch.current_quantity }};

            if (quantity <= 0) {
                alert('数量必须大于0');
                e.preventDefault();
                return false;
            }

            if (flowDirection === '减少' && quantity > currentStock) {
                alert('库存不足，当前库存: ' + currentStock + ' {{ batch.unit }}');
                e.preventDefault();
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
