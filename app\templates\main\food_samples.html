{% extends 'base.html' %}

{% block title %}留样管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>留样管理</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加留样记录
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>食谱名称</th>
                        <th>存储位置</th>
                        <th>留样开始时间</th>
                        <th>留样到期时间</th>
                        <th>操作人员</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sample in samples.items %}
                    <tr>
                        <td>{{ sample.id }}</td>
                        <td>{{ sample.recipe.name }}</td>
                        <td>{{ sample.storage_location }}</td>
                        <td>{{  sample.start_time|format_datetime('%Y-%m-%d %H:%M')   }}</td>
                        <td>{{  sample.end_time|format_datetime('%Y-%m-%d %H:%M')   }}</td>
                        <td>{{ sample.operator.real_name or sample.operator.username }}</td>
                        <td>
                            {% if sample.end_time > now %}
                            <span class="badge badge-success">有效</span>
                            {% else %}
                            <span class="badge badge-danger">已过期</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="#" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center">暂无留样记录</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% if samples.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not samples.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.food_samples', page=samples.prev_num) if samples.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in samples.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == samples.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.food_samples', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not samples.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.food_samples', page=samples.next_num) if samples.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
