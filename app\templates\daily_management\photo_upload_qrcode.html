{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .qrcode-card {
        border-radius: 0.5rem;
        box-shadow: 0 0.15rem 1.75rem rgba(58, 59, 69, 0.1);
        margin-bottom: 1.5rem;
    }

    .qrcode-card .card-header {
        background: linear-gradient(135deg, #f8f9fc, #ffffff);
        border-bottom: 1px solid rgba(227, 230, 240, 0.5);
        padding: 1rem 1.25rem;
    }

    .qrcode-container {
        text-align: center;
        padding: 2rem;
    }

    .qrcode-container img {
        max-width: 300px;
        height: auto;
        margin-bottom: 1.5rem;
        border: 1px solid #e3e6f0;
        padding: 0.5rem;
        border-radius: 0.5rem;
    }

    .qrcode-container h3 {
        margin-bottom: 1rem;
        color: #4e73df;
    }

    .qrcode-container p {
        color: #858796;
        margin-bottom: 1.5rem;
    }

    .qrcode-container .btn-group {
        margin-top: 1rem;
    }

    .qrcode-info {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .qrcode-info p {
        margin-bottom: 0.5rem;
    }

    .qrcode-info i {
        color: #4e73df;
        margin-right: 0.5rem;
    }

    @d-flex print {
        .no-print {
            display: none !important;
        }

        .qrcode-container {
            padding: 0;
        }

        .qrcode-container img {
            max-width: 100%;
            height: auto;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            padding: 0;
        }

        .qrcode-card {
            box-shadow: none;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>

        <div>
            <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i> 返回检查记录
            </a>

            <button type="button" class="btn btn-primary btn-sm" class="print-button">
                <i class="fas fa-print me-1"></i> 打印二维码
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="qrcode-card card">
                <div class="card-header no-print">
                    <h6 class="m-0 fw-bold text-primary">照片上传二维码</h6>
                </div>
                <div class="card-body">
                    <div class="qrcode-container">
                        <h3>{{ school.name }} - 检查照片上传</h3>
                        <p>日期：{{ log.log_date }}</p>

                        {% if qrcode_base64 %}
                        <img src="data:image/png;base64,{{ qrcode_base64 }}" alt="照片上传二维码">
                        {% else %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-1"></i> 二维码生成失败
                        </div>
                        {% endif %}

                        <p>请扫描上方二维码上传检查照片</p>

                        <div class="qrcode-info no-print">
                            <p><i class="fas fa-info-circle"></i> 员工可以通过扫描此二维码直接上传检查照片。</p>
                            <p><i class="fas fa-camera"></i> 支持拍照上传或从相册选择照片。</p>
                            <p><i class="fas fa-clock"></i> 可以选择早晨、中午或晚上的检查时段。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
