{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ user.username }} - 用户详情</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.edit_user', id=user.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑用户
        </a>
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal">
            <i class="fas fa-trash"></i> 删除用户
        </button>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>用户名</th>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <th>真实姓名</th>
                        <td>{{ user.real_name or '-' }}</td>
                    </tr>
                    <tr>
                        <th>电子邮箱</th>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <th>手机号码</th>
                        <td>{{ user.phone }}</td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>
                            {% if user.status == 1 %}
                            <span class="badge badge-success">启用</span>
                            {% else %}
                            <span class="badge badge-danger">禁用</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>最后登录</th>
                        <td>{{  user.last_login|format_datetime('%Y-%m-%d %H:%M') if user.last_login else '从未登录'   }}</td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{  user.created_at|format_datetime('%Y-%m-%d %H:%M')   }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">角色信息</h5>
            </div>
            <div class="card-body">
                {% if user.roles %}
                <ul class="list-group mb-3">
                    {% for role in user.roles %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        {{ role.name }}
                        <span class="badge badge-primary badge-pill">{{ role.description or '无描述' }}</span>
                    </li>
                    {% endfor %}
                </ul>

                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">权限详情</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover mb-0">
                                <thead class="thead-light">
                                    <tr>
                                        <th style="width: 150px;">模块</th>
                                        <th>权限</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set all_permissions = {} %}

                                    {# 合并所有角色的权限 #}
                                    {% for role in user.roles %}
                                        {% set role_permissions = role.permissions|fromjson %}
                                        {% for module, actions in role_permissions.items() %}
                                            {% if module not in all_permissions %}
                                                {% set _ = all_permissions.update({module: []}) %}
                                            {% endif %}
                                            {% for action in actions %}
                                                {% if action not in all_permissions[module] %}
                                                    {% set _ = all_permissions[module].append(action) %}
                                                {% endif %}
                                            {% endfor %}
                                        {% endfor %}
                                    {% endfor %}

                                    {# 如果有全局权限，直接显示 #}
                                    {% if '*' in all_permissions and '*' in all_permissions['*'] %}
                                        <tr>
                                            <td colspan="2" class="text-center bg-warning">
                                                <strong>系统管理员 - 拥有所有权限</strong>
                                            </td>
                                        </tr>
                                    {% else %}
                                        {# 用户管理 #}
                                        {% if 'user' in all_permissions %}
                                        <tr>
                                            <td class="bg-primary text-white">用户管理</td>
                                            <td>
                                                {% if '*' in all_permissions['user'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['user'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['user'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['user'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['user'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                    {% if 'change_status' in all_permissions['user'] %}<span class="badge badge-warning">修改状态</span>{% endif %}
                                                    {% if 'reset_password' in all_permissions['user'] %}<span class="badge badge-secondary">重置密码</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 角色管理 #}
                                        {% if 'role' in all_permissions %}
                                        <tr>
                                            <td class="bg-success text-white">角色管理</td>
                                            <td>
                                                {% if '*' in all_permissions['role'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['role'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['role'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['role'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['role'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 区域管理 #}
                                        {% if 'area' in all_permissions %}
                                        <tr>
                                            <td class="bg-info text-white">区域管理</td>
                                            <td>
                                                {% if '*' in all_permissions['area'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['area'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['area'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['area'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['area'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 供应商管理 #}
                                        {% if 'supplier' in all_permissions %}
                                        <tr>
                                            <td class="bg-warning text-white">供应商管理</td>
                                            <td>
                                                {% if '*' in all_permissions['supplier'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['supplier'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['supplier'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['supplier'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['supplier'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 食材管理 #}
                                        {% if 'ingredient' in all_permissions %}
                                        <tr>
                                            <td class="bg-secondary text-white">食材管理</td>
                                            <td>
                                                {% if '*' in all_permissions['ingredient'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['ingredient'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['ingredient'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['ingredient'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['ingredient'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 食谱管理 #}
                                        {% if 'menu' in all_permissions %}
                                        <tr>
                                            <td class="bg-dark text-white">食谱管理</td>
                                            <td>
                                                {% if '*' in all_permissions['menu'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['menu'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['menu'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['menu'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['menu'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                    {% if 'approve' in all_permissions['menu'] %}<span class="badge badge-warning">审核</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 留样管理 #}
                                        {% if 'sample' in all_permissions %}
                                        <tr>
                                            <td class="bg-light">留样管理</td>
                                            <td>
                                                {% if '*' in all_permissions['sample'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['sample'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'create' in all_permissions['sample'] %}<span class="badge badge-success">创建</span>{% endif %}
                                                    {% if 'edit' in all_permissions['sample'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                    {% if 'delete' in all_permissions['sample'] %}<span class="badge badge-danger">删除</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 系统设置 #}
                                        {% if 'setting' in all_permissions %}
                                        <tr>
                                            <td class="bg-primary text-white">系统设置</td>
                                            <td>
                                                {% if '*' in all_permissions['setting'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['setting'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'edit' in all_permissions['setting'] %}<span class="badge badge-primary">编辑</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 日志管理 #}
                                        {% if 'log' in all_permissions %}
                                        <tr>
                                            <td class="bg-secondary text-white">日志管理</td>
                                            <td>
                                                {% if '*' in all_permissions['log'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['log'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'export' in all_permissions['log'] %}<span class="badge badge-secondary">导出</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}

                                        {# 报表管理 #}
                                        {% if 'report' in all_permissions %}
                                        <tr>
                                            <td class="bg-info text-white">报表管理</td>
                                            <td>
                                                {% if '*' in all_permissions['report'] %}
                                                    <span class="badge badge-danger">所有操作</span>
                                                {% else %}
                                                    {% if 'view' in all_permissions['report'] %}<span class="badge badge-info">查看</span>{% endif %}
                                                    {% if 'export' in all_permissions['report'] %}<span class="badge badge-secondary">导出</span>{% endif %}
                                                    {% if 'print' in all_permissions['report'] %}<span class="badge badge-dark">打印</span>{% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {% if current_user.is_admin() or current_user.has_permission('role', 'edit') %}
                <div class="mt-3 text-end">
                    <a href="{{ url_for('system.edit_user_permissions', id=user.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> 编辑权限
                    </a>
                </div>
                {% endif %}

                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 该用户没有分配角色
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">区域信息</h5>
            </div>
            <div class="card-body">
                {% if user.area %}
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th>区域名称</th>
                                <td>{{ user.area.name }}</td>
                            </tr>
                            <tr>
                                <th>区域代码</th>
                                <td>{{ user.area.code }}</td>
                            </tr>
                            <tr>
                                <th>区域级别</th>
                                <td>{{ user.area.get_level_name() }}</td>
                            </tr>
                            <tr>
                                <th>上级区域</th>
                                <td>
                                    {% if user.area.parent %}
                                    {{ user.area.parent.name }}
                                    {% else %}
                                    <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">区域路径</h5>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb bg-white">
                                        {% for area in user.area.get_ancestors() %}
                                        <li class="breadcrumb-item">
                                            <a href="{{ url_for('area.view_area', id=area.id) }}">{{ area.name }}</a>
                                        </li>
                                        {% endfor %}
                                        <li class="breadcrumb-item active">{{ user.area.name }}</li>
                                    </ol>
                                </nav>
                                <a href="{{ url_for('area.view_area', id=user.area.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> 查看区域详情
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 该用户未关联任何区域
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">操作日志</h5>
            </div>
            <div class="card-body">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>操作类型</th>
                                <th>资源类型</th>
                                <th>区域</th>
                                <th>IP地址</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>
                                    {% if log.action == 'create' %}
                                    <span class="badge badge-success">创建</span>
                                    {% elif log.action == 'update' %}
                                    <span class="badge badge-primary">更新</span>
                                    {% elif log.action == 'delete' %}
                                    <span class="badge badge-danger">删除</span>
                                    {% elif log.action == 'view' %}
                                    <span class="badge badge-info">查看</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ log.action }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.resource_type }}</td>
                                <td>
                                    {% if log.area %}
                                    {{ log.area.name }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.ip_address or '-' }}</td>
                                <td>{{  log.created_at|format_datetime  }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 暂无操作日志
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<!-- 删除用户确认对话框 -->
{% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
<div class="modal fade" id="deleteUserModal" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteUserModalLabel">确认删除用户</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <strong>{{ user.username }}</strong> 吗？此操作不可逆！</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：删除用户将同时删除与该用户相关的所有数据和权限设置。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('system.delete_user', id=user.id) }}" method="post" novalidate novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}