{% extends 'base.html' %}

{% block title %}添加培训记录{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 增加字体大小 */
    body {
        font-size: 16px;
    }

    .h3, h3 {
        font-size: 1.8rem;
    }

    .form-control {
        font-size: 1rem;
    }

    label {
        font-size: 1.05rem;
        font-weight: 500;
    }

    .btn {
        font-size: 1rem;
    }

    .photo-preview {
        max-width: 150px;
        max-height: 150px;
        margin: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .preview-container {
        position: relative;
        display: inline-block;
        margin: 10px;
    }

    .preview-remove {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        cursor: pointer;
    }

    .form-section {
        background-color: #f8f9fc;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 25px;
        border-start: 4px solid #4e73df;
    }

    .section-title {
        font-weight: 600;
        color: #4e73df;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .custom-file-input:lang(zh) ~ .custom-file-label::after {
        content: "浏览";
    }

    .required-field::after {
        content: " *";
        color: #e74a3b;
    }

    .btn-submit {
        min-width: 140px;
        padding: 10px 20px;
        font-size: 1.1rem;
    }

    .form-text {
        font-size: 0.9rem;
    }

    /* 增强表单元素的可点击区域 */
    .mb-3 {
        margin-bottom: 1.5rem;
    }

    /* 增强视觉层次 */
    .card-header {
        padding: 1rem 1.25rem;
    }

    .card-body {
        padding: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'trainings') }}

    <!-- 页面标题和返回按钮 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus-circle me-1"></i> 添加培训记录
        </h1>
        <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> 返回培训记录列表
        </a>
    </div>

    <!-- 培训记录表单卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary"><i class="fas fa-chalkboard-teacher me-1"></i> 培训信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" id="trainingForm" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <!-- 基本信息部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-info-circle me-1"></i> 基本信息</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="required-field" for="training_topic">培训主题</label>
                                <input type="text" class="form-control" id="training_topic" name="training_topic" required
                                       placeholder="请输入培训主题">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="required-field" for="trainer">培训讲师</label>
                                <input type="text" class="form-control" id="trainer" name="trainer" required
                                       placeholder="请输入培训讲师姓名">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间地点部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-clock me-1"></i> 时间与地点</div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="required-field" for="training_date">培训日期</label>
                                <input type="date" class="form-control" id="training_date" name="training_date" required
                                       value="{{ now.strftime('%Y-%m-%d') }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="required-field" for="training_time">培训时间</label>
                                <input type="time" class="form-control" id="training_time" name="training_time" required
                                       value="{{ now.strftime('%H:%M') }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="location">培训地点</label>
                                <input type="text" class="form-control" id="location" name="location"
                                       placeholder="请输入培训地点">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration">培训时长(分钟)</label>
                                <input type="number" class="form-control" id="duration" name="duration" min="0"
                                       placeholder="请输入培训时长">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="attendees_count">参训人数</label>
                                <input type="number" class="form-control" id="attendees_count" name="attendees_count" min="0"
                                       placeholder="请输入参训人数">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 培训内容部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-book me-1"></i> 培训内容与评估</div>
                    <div class="mb-3">
                        <label for="content_summary">培训内容摘要</label>
                        <textarea class="form-control" id="content_summary" name="content_summary" rows="3"
                                  placeholder="请输入培训内容摘要..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="effectiveness_evaluation">效果评估</label>
                        <textarea class="form-control" id="effectiveness_evaluation" name="effectiveness_evaluation" rows="3"
                                  placeholder="请输入培训效果评估..."></textarea>
                    </div>
                </div>

                <!-- 照片上传部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-camera me-1"></i> 培训照片</div>
                    <div class="custom-file mb-3">
                        <input type="file" class="custom-file-input" id="photos" name="photos" multiple accept="image/*">
                        <label class="custom-file-label" for="photos">选择照片...</label>
                    </div>
                    <small class="form-text text-muted mb-3">
                        <i class="fas fa-info-circle"></i> 可以选择多张照片上传，支持jpg、jpeg、png格式，每张照片大小不超过5MB
                    </small>
                    <div id="photo-previews" class="d-flex flex-wrap"></div>
                </div>

                <!-- 提交按钮 -->
                <div class="mb-3 text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save me-1"></i> 保存培训记录
                    </button>
                    <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-1"></i> 取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 美化文件输入框
        $('.custom-file-input').on('change', function() {
            let fileName = $(this).val().split('\\').pop();
            if (fileName) {
                let fileCount = this.files.length;
                if (fileCount > 1) {
                    $(this).next('.custom-file-label').html(`已选择 ${fileCount} 张照片`);
                } else {
                    $(this).next('.custom-file-label').html(fileName);
                }
            } else {
                $(this).next('.custom-file-label').html('选择照片...');
            }
        });

        // 照片预览
        document.getElementById('photos').addEventListener('change', function(e) {
            const previewsDiv = document.getElementById('photo-previews');
            previewsDiv.innerHTML = '';

            for (const file of this.files) {
                // 检查文件大小
                if (file.size > 5 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过5MB，请选择较小的文件。`);
                    continue;
                }

                const previewContainer = document.createElement('div');
                previewContainer.className = 'preview-container';

                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.className = 'photo-preview';
                    img.title = file.name;

                    const removeBtn = document.createElement('div');
                    removeBtn.className = 'preview-remove';
                    removeBtn.innerHTML = '×';
                    removeBtn.title = '移除';

                    previewContainer.appendChild(img);
                    previewContainer.appendChild(removeBtn);
                    previewsDiv.appendChild(previewContainer);

                    // 添加移除功能
                    removeBtn.addEventListener('click', function() {
                        previewContainer.remove();
                    });
                }
                reader.readAsDataURL(file);
            }
        });

        // 表单验证
        $('#trainingForm').on('submit', function(e) {
            let isValid = true;

            // 检查必填字段
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('请填写所有必填字段');
                return false;
            }

            // 显示加载状态
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        });
    });
</script>
{% endblock %}
