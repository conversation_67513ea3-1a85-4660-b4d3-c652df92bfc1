{% extends 'base.html' %}

{% block title %}库存预警检查{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">库存预警检查</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory_alert.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回预警列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory_alert.check_alerts') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">检查预警</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 库存不足预警 -->
                    <div class="card card-danger">
                        <div class="card-header">
                            <h4 class="card-title">库存不足预警</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>区域</th>
                                            <th>食材名称</th>
                                            <th>当前库存</th>
                                            <th>预警阈值</th>
                                            <th>缺口</th>
                                            <th>预警级别</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in alerts_result.low_stock %}
                                        <tr>
                                            <td>{{ item.alert.area.name }}</td>
                                            <td>{{ item.alert.ingredient.name }}</td>
                                            <td>{{ item.current_stock }} {{ item.alert.ingredient.unit }}</td>
                                            <td>{{ item.alert.min_quantity }} {{ item.alert.ingredient.unit }}</td>
                                            <td>{{ item.shortage }} {{ item.alert.ingredient.unit }}</td>
                                            <td>
                                                {% if item.alert.alert_level == '紧急' %}
                                                <span class="badge badge-danger">紧急</span>
                                                {% elif item.alert.alert_level == '警告' %}
                                                <span class="badge badge-warning">警告</span>
                                                {% elif item.alert.alert_level == '提醒' %}
                                                <span class="badge badge-info">提醒</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('inventory.ingredient_inventory', id=item.alert.ingredient_id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看库存
                                                </a>
                                                <a href="{{ url_for('inventory_alert.create_requisition', id=item.alert.id) }}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-shopping-cart"></i> 创建采购申请
                                                </a>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无库存不足预警</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% if alerts_result.low_stock %}
                            <div class="mt-3">
                                <a href="{{ url_for('inventory_alert.batch_create_requisition') }}?{{  alerts_result.low_stock|map(attribute='alert.id')|map('string')|map('urlencode')|map('regex_replace', '^(.*)$', 'alert_ids[]=\\1')|join('&')  }}" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart"></i> 批量创建采购申请
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 临期预警 -->
                    <div class="card card-warning mt-4">
                        <div class="card-header">
                            <h4 class="card-title">临期预警</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>区域</th>
                                            <th>食材名称</th>
                                            <th>预警天数</th>
                                            <th>批次数量</th>
                                            <th>预警级别</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in alerts_result.expiring %}
                                        <tr>
                                            <td>{{ item.alert.area.name }}</td>
                                            <td>{{ item.alert.ingredient.name }}</td>
                                            <td>{{ item.days }} 天</td>
                                            <td>{{  item.items|length  }} 批次</td>
                                            <td>
                                                {% if item.alert.alert_level == '紧急' %}
                                                <span class="badge badge-danger">紧急</span>
                                                {% elif item.alert.alert_level == '警告' %}
                                                <span class="badge badge-warning">警告</span>
                                                {% elif item.alert.alert_level == '提醒' %}
                                                <span class="badge badge-info">提醒</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('inventory.ingredient_inventory', id=item.alert.ingredient_id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看库存
                                                </a>
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#expiringModal{{ loop.index }}">
                                                    <i class="fas fa-list"></i> 查看批次
                                                </button>
                                            </td>
                                        </tr>

                                        <!-- 临期批次详情模态框 -->
                                        <div class="modal fade" id="expiringModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="expiringModalLabel{{ loop.index }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="expiringModalLabel{{ loop.index }}">临期批次详情 - {{ item.alert.ingredient.name }}</h5>
                                                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <table class="table table-bordered table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>仓库</th>
                                                                    <th>存储位置</th>
                                                                    <th>批次号</th>
                                                                    <th>数量</th>
                                                                    <th>过期日期</th>
                                                                    <th>剩余天数</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {% for inventory in item.items %}
                                                                <tr>
                                                                    <td>{{ inventory.warehouse.name }}</td>
                                                                    <td>{{ inventory.storage_location.name }}</td>
                                                                    <td>{{ inventory.batch_number }}</td>
                                                                    <td>{{ inventory.quantity }} {{ inventory.unit }}</td>
                                                                    <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                                                    <td>
                                                                        {% set days_remaining = (inventory.expiry_date - date.today()).days %}
                                                                        <span class="badge {% if days_remaining <= 3 %}badge-danger{% elif days_remaining <= 7 %}badge-warning{% else %}badge-info{% endif %}">
                                                                            {{ days_remaining }} 天
                                                                        </span>
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">暂无临期预警</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 过期预警 -->
                    <div class="card card-danger mt-4">
                        <div class="card-header">
                            <h4 class="card-title">过期预警</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>批次数量</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in alerts_result.expired %}
                                        <tr>
                                            <td>{{ item.ingredient.name }}</td>
                                            <td>{{  item.items|length  }} 批次</td>
                                            <td>
                                                <a href="{{ url_for('inventory.ingredient_inventory', id=item.ingredient.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看库存
                                                </a>
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#expiredModal{{ loop.index }}">
                                                    <i class="fas fa-list"></i> 查看批次
                                                </button>
                                            </td>
                                        </tr>

                                        <!-- 过期批次详情模态框 -->
                                        <div class="modal fade" id="expiredModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="expiredModalLabel{{ loop.index }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="expiredModalLabel{{ loop.index }}">过期批次详情 - {{ item.ingredient.name }}</h5>
                                                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <table class="table table-bordered table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>仓库</th>
                                                                    <th>存储位置</th>
                                                                    <th>批次号</th>
                                                                    <th>数量</th>
                                                                    <th>过期日期</th>
                                                                    <th>过期天数</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {% for inventory in item.items %}
                                                                <tr>
                                                                    <td>{{ inventory.warehouse.name }}</td>
                                                                    <td>{{ inventory.storage_location.name }}</td>
                                                                    <td>{{ inventory.batch_number }}</td>
                                                                    <td>{{ inventory.quantity }} {{ inventory.unit }}</td>
                                                                    <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                                                    <td>
                                                                        {% set days_expired = (date.today() - inventory.expiry_date).days %}
                                                                        <span class="badge badge-danger">{{ days_expired }} 天</span>
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center">暂无过期预警</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
