{% extends 'base.html' %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
.table-fixed-header {
    overflow-y: auto;
    max-height: 500px;
}
.table-fixed-header thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
}
.selected-row {
    background-color: #e8f5e9 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">个性化调整产品</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- 步骤进度条 -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">创建产品批次流程</small>
                                    <small class="text-muted">步骤 4/5</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" class="w-80" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success">1. 基本信息</small>
                                    <small class="text-success">2. 选择食材</small>
                                    <small class="text-success">3. 设置属性</small>
                                    <small class="text-success fw-bold">4. 个性调整</small>
                                    <small class="text-muted">5. 确认创建</small>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <p>批次名称：{{ batch.name }}</p>
                                <p>分类：{{ batch.category.name if batch.category else '' }}</p>
                                <p>供应商：{{ batch.supplier.name if batch.supplier else '' }}</p>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-default" id="selectAll">全选</button>
                                        <button type="button" class="btn btn-default" id="deselectAll">取消全选</button>
                                    </div>

                                    <div class="btn-group ms-2">
                                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            批量设置单位
                                        </button>
                                        <div class="dropdown-menu">
                                            {% for unit in units %}
                                            <a class="dropdown-item set-unit" href="#" data-unit-id="{{ unit.id }}" data-unit-name="{{ unit.name }}">{{ unit.name }} ({{ unit.symbol }})</a>
                                            {% endfor %}
                                        </div>
                                    </div>

                                    <div class="input-group ms-2 d-inline-flex" style="width: 300px;">
                                        <input type="text" id="searchProduct" class="form-control" placeholder="搜索食材...">
                                        <div >
                                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <form method="post" id="productForm" novalidate novalidate>
                                {{ form.csrf_token }}
                                {{ form.batch_id }}
                                <input type="hidden" name="product_data" id="productData">

                                <div class="table-fixed-header">
                                    <table class="table table-bordered table-striped" id="productTable">
                                        <thead>
                                            <tr>
                                                <th width="40px"><input type="checkbox" id="checkAll"></th>
                                                <th>食材名称</th>
                                                <th width="120px">价格(元)</th>
                                                <th width="120px">规格值</th>
                                                <th width="150px">规格单位</th>
                                                <th width="200px">质量认证</th>
                                                <th width="120px">供货周期(天)</th>
                                                <th width="120px">最小订购量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for ingredient in ingredients %}
                                            <tr class="product-row" data-ingredient-id="{{ ingredient.id }}">
                                                <td>
                                                    <input type="checkbox" class="row-checkbox">
                                                </td>
                                                <td>
                                                    {{ ingredient.name }}
                                                    <input type="hidden" class="ingredient-id" value="{{ ingredient.id }}">
                                                    <input type="hidden" class="product-name" value="{{ ingredient.name }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm product-price" value="{{ attributes.fixed_price if attributes.price_strategy == 'fixed' else '' }}" step="0.01" min="0">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm specification-value" value="1" step="0.01" min="0">
                                                </td>
                                                <td>
                                                    <select class="form-control form-control-sm unit-id">
                                                        {% for unit in units %}
                                                        <option value="{{ unit.id }}" {% if unit.id == default_unit.id %}selected{% endif %}>{{ unit.name }} ({{ unit.symbol }})</option>
                                                        {% endfor %}
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm quality-cert" value="{{ attributes.quality_cert }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm lead-time" value="{{ attributes.lead_time }}" min="0">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm min-order-quantity" value="{{ attributes.min_order_quantity }}" step="0.01" min="0">
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <div class="mb-3 text-center mt-3">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.set_attributes', id=batch.id) }}" class="btn btn-default">返回上一步</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 搜索产品
    $('#searchProduct').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.product-row').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // 清除搜索
    $('#clearSearch').click(function() {
        $('#searchProduct').val('');
        $('.product-row').show();
    });

    // 全选/取消全选
    $('#checkAll').change(function() {
        $('.row-checkbox:visible').prop('checked', $(this).prop('checked'));
        updateSelectedRows();
    });

    // 选择所有行
    $('#selectAll').click(function() {
        $('.row-checkbox:visible').prop('checked', true);
        updateSelectedRows();
    });

    // 取消选择所有行
    $('#deselectAll').click(function() {
        $('.row-checkbox:visible').prop('checked', false);
        updateSelectedRows();
    });

    // 行选择
    $('.row-checkbox').change(function() {
        updateSelectedRows();
    });

    // 更新选中行的样式
    function updateSelectedRows() {
        $('.row-checkbox').each(function() {
            if ($(this).prop('checked')) {
                $(this).closest('tr').addClass('selected-row');
            } else {
                $(this).closest('tr').removeClass('selected-row');
            }
        });
    }

    // 批量设置单位
    $('.set-unit').click(function(e) {
        e.preventDefault();
        var unitId = $(this).data('unit-id');
        var unitName = $(this).data('unit-name');

        $('.row-checkbox:checked').each(function() {
            $(this).closest('tr').find('.unit-id').val(unitId);
        });
    });

    // 表单提交前收集产品数据
    $('#productForm').submit(function() {
        var productData = [];

        $('.product-row').each(function() {
            var row = $(this);
            var ingredientId = row.find('.ingredient-id').val();
            var productName = row.find('.product-name').val();
            var price = row.find('.product-price').val();
            var specificationValue = row.find('.specification-value').val();
            var unitId = row.find('.unit-id').val();
            var qualityCert = row.find('.quality-cert').val();
            var leadTime = row.find('.lead-time').val();
            var minOrderQuantity = row.find('.min-order-quantity').val();

            productData.push({
                ingredient_id: parseInt(ingredientId),
                product_name: productName,
                product_code: '',
                model_number: '',
                price: parseFloat(price),
                specification_value: parseFloat(specificationValue),
                unit_id: parseInt(unitId),
                quality_cert: qualityCert,
                quality_standard: '{{ attributes.quality_standard }}',
                lead_time: parseInt(leadTime),
                min_order_quantity: parseFloat(minOrderQuantity),
                description: ''
            });
        });

        $('#productData').val(JSON.stringify(productData));
        return true;
    });

    // 初始化
    updateSelectedRows();
});
</script>
{% endblock %}
