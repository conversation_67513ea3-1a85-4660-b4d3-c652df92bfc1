{% macro enhanced_image_uploader(reference_type, reference_id, title="照片上传", max_files=10, max_file_size=5, show_rating=True) %}
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 fw-bold text-primary">{{ title }}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">操作:</div>
                <a class="dropdown-item refresh-images" href="javascript:void(0);">
                    <i class="fas fa-sync-alt fa-sm fa-fw me-2 text-gray-400"></i>刷新图片
                </a>
                <a class="dropdown-item clear-images" href="javascript:void(0);">
                    <i class="fas fa-trash fa-sm fa-fw me-2 text-gray-400"></i>清空预览
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#helpModal">
                    <i class="fas fa-question-circle fa-sm fa-fw me-2 text-gray-400"></i>使用帮助
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="enhanced-uploader-{{ reference_type }}-{{ reference_id }}" class="enhanced-image-uploader"></div>
    </div>
</div>

<!-- 帮助模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1" role="dialog" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">照片上传使用帮助</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <h6 class="fw-bold">基本操作</h6>
                <ul>
                    <li>点击或拖拽图片到上传区域上传照片</li>
                    <li>最多可上传 {{ max_files }} 张照片</li>
                    <li>每张照片大小不超过 {{ max_file_size }}MB</li>
                    <li>支持 JPG、PNG、GIF 格式</li>
                </ul>
                
                <h6 class="fw-bold">照片管理</h6>
                <ul>
                    <li>鼠标悬停在照片上可以看到操作按钮</li>
                    <li>点击 <i class="fas fa-eye"></i> 查看大图</li>
                    <li>点击 <i class="fas fa-trash"></i> 删除照片</li>
                    {% if show_rating %}
                    <li>点击星星可以为照片评分（1-5星）</li>
                    {% endif %}
                </ul>
                
                <h6 class="fw-bold">提示</h6>
                <ul>
                    <li>上传过程中请勿关闭页面</li>
                    <li>如果上传失败，请尝试刷新页面后重新上传</li>
                    <li>照片上传后会自动保存到服务器</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已加载增强图片上传器脚本
        if (typeof EnhancedImageUploader === 'undefined') {
            // 加载增强图片上传器脚本
            const script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/enhanced-image-uploader.js') }}";
            script.onload = initUploader;
            document.head.appendChild(script);
        } else {
            initUploader();
        }
        
        function initUploader() {
            const container = document.getElementById('enhanced-uploader-{{ reference_type }}-{{ reference_id }}');
            if (container) {
                // 创建上传器实例
                const uploader = new EnhancedImageUploader(container, {
                    referenceType: '{{ reference_type }}',
                    referenceId: {{ reference_id }},
                    apiBaseUrl: '/daily-management/image-api',
                    maxFiles: {{ max_files }},
                    maxFileSize: {{ max_file_size }},
                    showRating: {{ 'true' if show_rating else 'false' }},
                    previewSize: 150
                });
                
                // 保存实例到容器
                container.uploader = uploader;
                
                // 绑定刷新按钮
                const refreshBtn = document.querySelector('.refresh-images');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', function() {
                        if (container.uploader) {
                            // 重新加载图片
                            container.uploader.destroy();
                            initUploader();
                        }
                    });
                }
                
                // 绑定清空按钮
                const clearBtn = document.querySelector('.clear-images');
                if (clearBtn) {
                    clearBtn.addEventListener('click', function() {
                        if (container.uploader) {
                            // 清空预览
                            container.uploader.destroy();
                            initUploader();
                        }
                    });
                }
            }
        }
    });
</script>
{% endmacro %}
