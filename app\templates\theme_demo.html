{% extends "base.html" %}

{% block title %}主题配色演示 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-palette"></i> 主题配色方案演示
                    </h4>
                    <p class="mb-0 mt-2">
                        <small class="text-white-50">
                            展示系统支持的所有配色方案，包括现代专业系列和经典优雅系列
                        </small>
                    </p>
                </div>
                <div class="card-body">
                    <!-- 现代专业系列 -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-laptop"></i> 现代专业系列
                                <small class="text-muted">适合商务办公和专业应用</small>
                            </h5>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="primary">
                                <div class="card-header">
                                    <span class="theme-preview primary"></span>
                                    🌊 海洋蓝主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">专业、信任、稳定</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="primary">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="secondary">
                                <div class="card-header">
                                    <span class="theme-preview secondary"></span>
                                    🔘 现代灰主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">简约、专业、平衡</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="secondary">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="success">
                                <div class="card-header">
                                    <span class="theme-preview success"></span>
                                    🌿 自然绿主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">健康、成长、和谐</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="success">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="warning">
                                <div class="card-header">
                                    <span class="theme-preview warning"></span>
                                    🔥 活力橙主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">活力、创新、温暖</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="warning">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="info">
                                <div class="card-header">
                                    <span class="theme-preview info"></span>
                                    💜 优雅紫主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">创新、优雅、神秘</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="info">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="danger">
                                <div class="card-header">
                                    <span class="theme-preview danger"></span>
                                    ❤️ 深邃红主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">力量、重要、警示</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="danger">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="dark">
                                <div class="card-header">
                                    <span class="theme-preview dark"></span>
                                    🌙 深色主题
                                </div>
                                <div class="card-body">
                                    <p class="card-text">现代、护眼、专业</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="dark">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 经典优雅系列 -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-crown"></i> 经典优雅系列
                                <small class="text-muted">经过时间沉淀的经典配色，适合高端场景</small>
                            </h5>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="classic-neutral">
                                <div class="card-header">
                                    <span class="theme-preview classic-neutral"></span>
                                    🏛️ 经典中性风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">米白色·深棕色·金色<br>温暖优雅</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="classic-neutral">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="modern-neutral">
                                <div class="card-header">
                                    <span class="theme-preview modern-neutral"></span>
                                    🏢 现代中性风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">灰白色·深灰色·香槟金<br>简洁高雅</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="modern-neutral">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="vintage-elegant">
                                <div class="card-header">
                                    <span class="theme-preview vintage-elegant"></span>
                                    🍷 复古典雅风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">酒红色·墨绿色·金色<br>浓郁醇厚</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="vintage-elegant">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="noble-elegant">
                                <div class="card-header">
                                    <span class="theme-preview noble-elegant"></span>
                                    👑 贵族典雅风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">藏青色·深紫色·银色<br>深邃高贵</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="noble-elegant">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="fresh-elegant">
                                <div class="card-header">
                                    <span class="theme-preview fresh-elegant"></span>
                                    🌸 清新优雅风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">淡蓝色·淡粉色·米白色<br>温馨浪漫</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="fresh-elegant">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="spring-fresh">
                                <div class="card-header">
                                    <span class="theme-preview spring-fresh"></span>
                                    🌱 春日清新风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">薄荷绿·浅黄色·象牙白<br>清爽宜人</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="spring-fresh">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="luxury-solemn">
                                <div class="card-header">
                                    <span class="theme-preview luxury-solemn"></span>
                                    ✨ 华丽庄重风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">金色·黑色·红色<br>华丽高贵</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="luxury-solemn">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100" data-theme="royal-solemn">
                                <div class="card-header">
                                    <span class="theme-preview royal-solemn"></span>
                                    🎭 皇室庄重风
                                </div>
                                <div class="card-body">
                                    <p class="card-text">深紫色·深红色·黑色<br>神秘庄重</p>
                                    <button class="btn btn-primary btn-sm theme-demo-btn" data-theme="royal-solemn">
                                        预览主题
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 表格演示 -->
                    <div class="row mt-5">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-table"></i> 表格样式演示
                                <small class="text-muted">优化后的表格排版效果</small>
                            </h5>
                            
                            <div class="table-toolbar">
                                <div class="toolbar-left">
                                    <h6 class="mb-0">
                                        <i class="fas fa-list"></i> 演示数据表格
                                        <span class="badge badge-primary ms-2">3 条记录</span>
                                    </h6>
                                </div>
                                <div class="toolbar-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        表格已优化显示，支持紧凑排版
                                    </small>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover table-compact">
                                    <thead>
                                        <tr>
                                            <th style="width: 140px;">编号</th>
                                            <th style="width: 120px;">名称</th>
                                            <th style="width: 120px;">创建时间</th>
                                            <th style="width: 90px;" class="number-column">金额</th>
                                            <th style="width: 80px;">状态</th>
                                            <th style="width: 150px;" class="action-column">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><span class="font-weight-medium">DEMO-001</span></td>
                                            <td class="text-content">演示项目一</td>
                                            <td class="datetime-column">
                                                <div class="datetime-display">
                                                    <span class="date">12-25</span>
                                                    <span class="time">14:30</span>
                                                </div>
                                            </td>
                                            <td class="number-column">
                                                <span class="amount-display">¥1,234.56</span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-success">已完成</span>
                                            </td>
                                            <td class="action-column">
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="查看">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><span class="font-weight-medium">DEMO-002</span></td>
                                            <td class="text-content">演示项目二</td>
                                            <td class="datetime-column">
                                                <div class="datetime-display">
                                                    <span class="date">12-24</span>
                                                    <span class="time">09:15</span>
                                                </div>
                                            </td>
                                            <td class="number-column">
                                                <span class="amount-display">¥2,567.89</span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-warning">进行中</span>
                                            </td>
                                            <td class="action-column">
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="查看">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><span class="font-weight-medium">DEMO-003</span></td>
                                            <td class="text-content">演示项目三</td>
                                            <td class="datetime-column">
                                                <div class="datetime-display">
                                                    <span class="date">12-23</span>
                                                    <span class="time">16:45</span>
                                                </div>
                                            </td>
                                            <td class="number-column">
                                                <span class="amount-display">¥890.12</span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-danger">已取消</span>
                                            </td>
                                            <td class="action-column">
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="查看">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" title="恢复">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 主题预览功能
    $('.theme-demo-btn').click(function() {
        const theme = $(this).data('theme');
        if (window.themeSwitcher) {
            window.themeSwitcher.previewTheme(theme);
            
            // 显示预览提示
            toastr.info(`正在预览 ${$(this).closest('.card').find('.card-header').text().trim()} 主题，3秒后恢复`, '主题预览');
        }
    });
});
</script>
{% endblock %}
