{% extends 'base.html' %}

{% block title %}编辑问题记录{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">编辑问题记录</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">问题信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="issue_type">问题类型 <span class="text-danger">*</span></label>
                            <select class="form-control" id="issue_type" name="issue_type" required>
                                <option value="">请选择问题类型</option>
                                <option value="设备故障" {% if issue.issue_type == '设备故障' %}selected{% endif %}>设备故障</option>
                                <option value="卫生问题" {% if issue.issue_type == '卫生问题' %}selected{% endif %}>卫生问题</option>
                                <option value="食材问题" {% if issue.issue_type == '食材问题' %}selected{% endif %}>食材问题</option>
                                <option value="人员问题" {% if issue.issue_type == '人员问题' %}selected{% endif %}>人员问题</option>
                                <option value="安全隐患" {% if issue.issue_type == '安全隐患' %}selected{% endif %}>安全隐患</option>
                                <option value="其他" {% if issue.issue_type == '其他' %}selected{% endif %}>其他</option>
                                {% if issue.issue_type not in ['设备故障', '卫生问题', '食材问题', '人员问题', '安全隐患', '其他'] %}
                                <option value="{{ issue.issue_type }}" selected>{{ issue.issue_type }}</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="found_date">发现日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="found_date" name="found_date" value="{{ issue.found_time|format_datetime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="found_time">发现时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="found_time" name="found_time" value="{{ issue.found_time|format_datetime('%H:%M') }}" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description">问题描述 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="3" required>{{ issue.description }}</textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="responsible_person">责任人</label>
                            <input type="text" class="form-control" id="responsible_person" name="responsible_person" value="{{ issue.responsible_person or '' }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status">状态 <span class="text-danger">*</span></label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="pending" {% if issue.status == 'pending' %}selected{% endif %}>待处理</option>
                                <option value="fixing" {% if issue.status == 'fixing' %}selected{% endif %}>处理中</option>
                                <option value="fixed" {% if issue.status == 'fixed' %}selected{% endif %}>已修复</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="verification_result">验证结果</label>
                    <textarea class="form-control" id="verification_result" name="verification_result" rows="3">{{ issue.verification_result or '' }}</textarea>
                    <small class="form-text text-muted">问题修复后的验证结果，确认问题是否已解决</small>
                </div>

                <!-- 现有照片 -->
                <div class="mb-3">
                    <label>现有照片</label>
                    <div class="photo-gallery">
                        {% set photos = issue.photos %}
                        {% if photos %}
                            {% for photo in photos %}
                                <div class="photo-item">
                                    <a href="{{ photo.file_path }}" target="_blank">
                                        <img src="{{ photo.file_path }}" alt="问题照片">
                                    </a>
                                    <div class="photo-caption">
                                        {{ photo.description or '无描述' }}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">暂无照片</p>
                        {% endif %}
                    </div>
                </div>

                <div class="mb-3">
                    <label for="photos">上传新照片</label>
                    <input type="file" class="form-control-file" id="photos" name="photos" multiple accept="image/*">
                    <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                </div>

                <div class="mb-3 mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.view_issue', issue_id=issue.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const previewsDiv = document.getElementById('photo-previews');
        previewsDiv.innerHTML = '';

        for (const file of this.files) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview me-2 mb-2';
                previewsDiv.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
