{% extends "base.html" %}

{% block title %}数据修复{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">数据修复</h3>
        </div>
        <div class="card-body">
          <p class="text-muted">检查并修复数据库中的错误或缺失的数据。</p>
          
          <div class="row mb-4">
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body">
                  <h5 class="card-title">修复缺失数据</h5>
                  <p class="card-text">检查并修复失效的数据关联。</p>
                  <button id="checkMissingDataBtn" class="btn btn-primary">
                    <i class="fas fa-search"></i> 停留缺失数据
                  </button>
                </div>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body">
                  <h5 class="card-title">数据修复工具</h5>
                  <p class="card-text">使用专门的工具修复特定问题。</p>
                  <a href="{{ url_for('data_repair.repair_tools') }}" class="btn btn-success">
                    <i class="fas fa-tools"></i> 数据修复工具
                  </a>
                </div>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body">
                  <h5 class="card-title">数据完整性检查</h5>
                  <p class="card-text">检查数据库中的数据完整性。</p>
                  <button id="checkIntegrityBtn" class="btn btn-info">
                    <i class="fas fa-check-circle"></i> 检查数据完整性
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 检查结果区域 -->
          <div id="checkResultsArea" class="mt-4" style="display: none;">
            <div class="card">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0">检查结果</h5>
              </div>
              <div class="card-body">
                <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                  <li class="nav-item">
                    <a class="nav-link active" id="recipes-tab" data-bs-toggle="tab" href="#recipes" role="tab">
                      周菜单食谱 <span id="recipesBadge" class="badge badge-danger">0</span>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="ingredients-tab" data-bs-toggle="tab" href="#ingredients" role="tab">
                      食谱食材 <span id="ingredientsBadge" class="badge badge-danger">0</span>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="suppliers-tab" data-bs-toggle="tab" href="#suppliers" role="tab">
                      采购订单供应商 <span id="suppliersBadge" class="badge badge-danger">0</span>
                    </a>
                  </li>
                </ul>
                
                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="resultTabsContent">
                  <div class="tab-pane fade show active" id="recipes" role="tabpanel">
                    <div class="alert alert-info" id="recipesEmpty">
                      未发现周菜单中缺失的食谱。
                    </div>
                    <div id="recipesTable" style="display: none;">
                      <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>周菜单ID</th>
                              <th>周开始日期</th>
                              <th>食谱ID</th>
                              <th>食谱名称</th>
                            </tr>
                          </thead>
                          <tbody id="recipesTableBody">
                          </tbody>
                        </table>
                      </div>
                      <button id="fixRecipesBtn" class="btn btn-warning">
                        <i class="fas fa-wrench"></i> 修复周菜单食谱
                      </button>
                    </div>
                  </div>
                  
                  <div class="tab-pane fade" id="ingredients" role="tabpanel">
                    <div class="alert alert-info" id="ingredientsEmpty">
                      未发现食谱中缺失的食材。
                    </div>
                    <div id="ingredientsTable" style="display: none;">
                      <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>食谱ID</th>
                              <th>食谱名称</th>
                              <th>食材ID</th>
                            </tr>
                          </thead>
                          <tbody id="ingredientsTableBody">
                          </tbody>
                        </table>
                      </div>
                      <button id="fixIngredientsBtn" class="btn btn-warning">
                        <i class="fas fa-wrench"></i> 修复食谱食材
                      </button>
                    </div>
                  </div>
                  
                  <div class="tab-pane fade" id="suppliers" role="tabpanel">
                    <div class="alert alert-info" id="suppliersEmpty">
                      未发现采购订单中缺失的供应商。
                    </div>
                    <div id="suppliersTable" style="display: none;">
                      <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>订单编号</th>
                              <th>供应商ID</th>
                            </tr>
                          </thead>
                          <tbody id="suppliersTableBody">
                          </tbody>
                        </table>
                      </div>
                      <button id="fixSuppliersBtn" class="btn btn-warning">
                        <i class="fas fa-wrench"></i> 修复采购订单供应商
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 检查缺失数据
    $('#checkMissingDataBtn').click(function() {
      $(this).prop('disabled', true);
      $(this).html('<i class="fas fa-spinner fa-spin"></i> 检查中...');
      
      $.ajax({
        url: '{{ url_for("data_repair.check_missing_data") }}',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            displayCheckResults(response.data);
          } else {
            toastr.error(response.message || '检查失败');
          }
        },
        error: function(xhr, status, error) {
          toastr.error('请求失败: ' + error);
        },
        complete: function() {
          $('#checkMissingDataBtn').prop('disabled', false);
          $('#checkMissingDataBtn').html('<i class="fas fa-search"></i> 检查缺失数据');
        }
      });
    });
    
    // 修复周菜单食谱
    $('#fixRecipesBtn').click(function() {
      fixMissingData('fix_recipes', '修复周菜单食谱');
    });
    
    // 修复食谱食材
    $('#fixIngredientsBtn').click(function() {
      fixMissingData('fix_ingredients', '修复食谱食材');
    });
    
    // 修复采购订单供应商
    $('#fixSuppliersBtn').click(function() {
      fixMissingData('fix_suppliers', '修复采购订单供应商');
    });
    
    // 检查数据完整性
    $('#checkIntegrityBtn').click(function() {
      toastr.info('数据完整性检查功能正在开发中...');
    });
    
    // 显示检查结果
    function displayCheckResults(data) {
      // 显示结果区域
      $('#checkResultsArea').show();
      
      // 周菜单食谱
      const missingRecipes = data.missing_recipes || [];
      $('#recipesBadge').text(missingRecipes.length);
      
      if (missingRecipes.length > 0) {
        $('#recipesEmpty').hide();
        $('#recipesTable').show();
        
        const tbody = $('#recipesTableBody');
        tbody.empty();
        
        missingRecipes.forEach(function(item) {
          tbody.append(`
            <tr>
              <td>${item.id}</td>
              <td>${item.weekly_menu_id}</td>
              <td>${item.week_start || '-'}</td>
              <td>${item.recipe_id}</td>
              <td>${item.recipe_name || '-'}</td>
            </tr>
          `);
        });
      } else {
        $('#recipesEmpty').show();
        $('#recipesTable').hide();
      }
      
      // 食谱食材
      const missingIngredients = data.missing_ingredients || [];
      $('#ingredientsBadge').text(missingIngredients.length);
      
      if (missingIngredients.length > 0) {
        $('#ingredientsEmpty').hide();
        $('#ingredientsTable').show();
        
        const tbody = $('#ingredientsTableBody');
        tbody.empty();
        
        missingIngredients.forEach(function(item) {
          tbody.append(`
            <tr>
              <td>${item.id}</td>
              <td>${item.recipe_id}</td>
              <td>${item.recipe_name || '-'}</td>
              <td>${item.ingredient_id}</td>
            </tr>
          `);
        });
      } else {
        $('#ingredientsEmpty').show();
        $('#ingredientsTable').hide();
      }
      
      // 采购订单供应商
      const missingSuppliers = data.missing_suppliers || [];
      $('#suppliersBadge').text(missingSuppliers.length);
      
      if (missingSuppliers.length > 0) {
        $('#suppliersEmpty').hide();
        $('#suppliersTable').show();
        
        const tbody = $('#suppliersTableBody');
        tbody.empty();
        
        missingSuppliers.forEach(function(item) {
          tbody.append(`
            <tr>
              <td>${item.id}</td>
              <td>${item.order_number || '-'}</td>
              <td>${item.supplier_id}</td>
            </tr>
          `);
        });
      } else {
        $('#suppliersEmpty').show();
        $('#suppliersTable').hide();
      }
    }
    
    // 修复缺失数据
    function fixMissingData(fixType, actionName) {
      if (!confirm(`确定要${actionName}吗？此操作不可撤销。`)) {
        return;
      }
      
      const data = {};
      data[fixType] = true;
      
      $.ajax({
        url: '{{ url_for("data_repair.fix_missing_data") }}',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            toastr.success(response.message || '修复成功');
            // 重新检查数据
            $('#checkMissingDataBtn').click();
          } else {
            toastr.error(response.message || '修复失败');
          }
        },
        error: function(xhr, status, error) {
          toastr.error('请求失败: ' + error);
        }
      });
    }
  });
</script>
{% endblock %}
