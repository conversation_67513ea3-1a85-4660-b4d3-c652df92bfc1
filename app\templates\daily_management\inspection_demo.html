{% extends 'base.html' %}

{% block title %}检查项展示示例{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">检查项展示示例</h1>
        <a href="{{ url_for('daily_management.daily_logs') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-list fa-sm text-white-50"></i> 查看所有记录
        </a>
    </div>

    <!-- 内容行 -->
    <div class="row">
        <!-- 左侧列 - 指定ID的检查项 -->
        <div class="col-lg-6">
            <div class="mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">指定ID的检查项展示</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">
                            这个示例展示了如何通过指定检查项ID来展示检查项详情。
                            适用于需要展示特定检查项的场景，如详情页面。
                        </p>
                        
                        <div class="code-example mb-3">
                            <pre><code>{% raw %}{% include 'daily_management/inspection_widget.html' with 
    widget_id='specific', 
    inspection_id=1, 
    title='指定ID的检查项' 
%}{% endraw %}</code></pre>
                        </div>
                        
                        <!-- 实际示例 -->
                        {% include 'daily_management/inspection_widget.html' with 
                            widget_id='specific', 
                            inspection_id=1, 
                            title='指定ID的检查项' 
                        %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧列 - 最新的检查项 -->
        <div class="col-lg-6">
            <div class="mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">最新的检查项展示</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">
                            这个示例展示了如何获取最新的检查项详情。
                            适用于首页或仪表盘，展示最新的检查状态。
                        </p>
                        
                        <div class="code-example mb-3">
                            <pre><code>{% raw %}{% include 'daily_management/inspection_widget.html' with 
    widget_id='latest', 
    inspection_type='morning', 
    inspection_item='地面卫生', 
    days=7, 
    title='最近7天的晨检-地面卫生' 
%}{% endraw %}</code></pre>
                        </div>
                        
                        <!-- 实际示例 -->
                        {% include 'daily_management/inspection_widget.html' with 
                            widget_id='latest', 
                            inspection_type='morning', 
                            inspection_item='地面卫生', 
                            days=7, 
                            title='最近7天的晨检-地面卫生' 
                        %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第二行 - 多个检查项并排展示 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">多个检查项并排展示</h6>
                </div>
                <div class="card-body">
                    <p class="mb-3">
                        这个示例展示了如何在一行中并排展示多个检查项。
                        适用于仪表盘或概览页面，同时展示多个关键检查项。
                    </p>
                    
                    <div class="row">
                        <!-- 晨检-地面卫生 -->
                        <div class="col-md-4">
                            {% include 'daily_management/inspection_widget.html' with 
                                widget_id='morning-floor', 
                                inspection_type='morning', 
                                inspection_item='地面卫生', 
                                days=3, 
                                title='晨检-地面卫生' 
                            %}
                        </div>
                        
                        <!-- 午检-餐具消毒 -->
                        <div class="col-md-4">
                            {% include 'daily_management/inspection_widget.html' with 
                                widget_id='noon-tableware', 
                                inspection_type='noon', 
                                inspection_item='餐具消毒', 
                                days=3, 
                                title='午检-餐具消毒' 
                            %}
                        </div>
                        
                        <!-- 晚检-食品留样 -->
                        <div class="col-md-4">
                            {% include 'daily_management/inspection_widget.html' with 
                                widget_id='evening-sample', 
                                inspection_type='evening', 
                                inspection_item='食品留样', 
                                days=3, 
                                title='晚检-食品留样' 
                            %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 可以在这里添加额外的JavaScript逻辑
    });
</script>
{% endblock %}
