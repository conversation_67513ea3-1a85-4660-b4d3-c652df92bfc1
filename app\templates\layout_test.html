{% extends 'base.html' %}

{% block title %}左右布局测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">左右布局测试页面</h3>
                    <small class="text-muted">测试新的左右布局结构是否正常工作</small>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 布局测试</h5>
                        <p>这是一个测试页面，用于验证左右布局的功能：</p>
                        <ul>
                            <li>✅ 左侧导航栏固定宽度200px</li>
                            <li>✅ 右侧内容区域自适应</li>
                            <li>✅ 顶部工具栏固定</li>
                            <li>✅ 底部信息栏</li>
                            <li>✅ 移动端响应式设计</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>功能测试</h5>
                                </div>
                                <div class="card-body">
                                    <p>请测试以下功能：</p>
                                    <div class="list-group">
                                        <div class="list-group-item">
                                            <strong>导航菜单</strong><br>
                                            <small>点击左侧导航菜单项，确保页面正常跳转</small>
                                        </div>
                                        <div class="list-group-item">
                                            <strong>主题切换</strong><br>
                                            <small>点击顶部主题切换按钮，测试主题变化</small>
                                        </div>
                                        <div class="list-group-item">
                                            <strong>响应式设计</strong><br>
                                            <small>调整浏览器窗口大小，测试移动端适配</small>
                                        </div>
                                        <div class="list-group-item">
                                            <strong>内容滚动</strong><br>
                                            <small>确保内容区域可以正常滚动，不会超出屏幕</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>布局信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>左侧导航栏宽度</strong></td>
                                            <td>200px (固定)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>顶部工具栏高度</strong></td>
                                            <td>60px (固定)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>右侧内容区域</strong></td>
                                            <td>自适应宽度</td>
                                        </tr>
                                        <tr>
                                            <td><strong>移动端断点</strong></td>
                                            <td>768px</td>
                                        </tr>
                                        <tr>
                                            <td><strong>侧边栏层级</strong></td>
                                            <td>z-index: 1020</td>
                                        </tr>
                                        <tr>
                                            <td><strong>工具栏层级</strong></td>
                                            <td>z-index: 1030</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>测试内容</h5>
                                </div>
                                <div class="card-body">
                                    <p>这里是一些测试内容，用于验证页面布局和滚动功能。</p>
                                    
                                    {% for i in range(1, 21) %}
                                    <div class="alert alert-light">
                                        <strong>测试段落 {{ i }}</strong><br>
                                        这是第{{ i }}个测试段落，用于测试页面的滚动功能。内容应该在右侧内容区域内正常显示，不会超出屏幕边界。左侧导航栏应该保持固定位置，顶部工具栏也应该保持固定。
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 测试页面专用样式 */
.test-highlight {
    border: 2px dashed #007bff;
    padding: 10px;
    margin: 10px 0;
    background: rgba(0, 123, 255, 0.1);
}

.layout-info {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin: 15px 0;
}
</style>

<script>
// 测试页面专用脚本
document.addEventListener('DOMContentLoaded', function() {
    console.log('左右布局测试页面已加载');
    console.log('窗口宽度:', window.innerWidth);
    console.log('窗口高度:', window.innerHeight);
    
    // 检测布局元素
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const topToolbar = document.querySelector('.top-toolbar');
    
    if (sidebar) {
        console.log('✅ 侧边栏元素存在');
        console.log('侧边栏宽度:', sidebar.offsetWidth);
    } else {
        console.log('❌ 侧边栏元素不存在');
    }
    
    if (mainContent) {
        console.log('✅ 主内容区域存在');
        console.log('主内容区域左边距:', getComputedStyle(mainContent).marginLeft);
    } else {
        console.log('❌ 主内容区域不存在');
    }
    
    if (topToolbar) {
        console.log('✅ 顶部工具栏存在');
        console.log('工具栏高度:', topToolbar.offsetHeight);
    } else {
        console.log('❌ 顶部工具栏不存在');
    }
});
</script>
{% endblock %}
