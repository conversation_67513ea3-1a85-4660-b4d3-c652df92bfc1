/* 全局样式 - 适配左右布局 */
body {
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* 移除原有的flex布局，由sidebar-layout.css接管 */

/* 品牌样式 */
.navbar-brand {
    font-weight: bold;
}

/* 导航菜单样式 - 保留用于其他组件 */
.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    transition: color 0.2s;
}

.nav-link.active {
    color: #fff !important;
    font-weight: bold;
}

.nav-link .fas,
.nav-link .far {
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.2s;
}

.dropdown-item.active,
.dropdown-item:active {
    background-color: #007bff;
    color: white;
}

.dropdown-item .fas,
.dropdown-item .far {
    width: 20px;
    text-align: center;
    margin-right: 8px;
    color: #6c757d;
}

.dropdown-item.active .fas,
.dropdown-item.active .far,
.dropdown-item:active .fas,
.dropdown-item:active .far {
    color: white;
}

/* 卡片样式 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    border: none;
}

/* .card-header 样式已移至 theme-colors.css 统一管理 */

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 0.25rem;
}

/* 表格样式 - 基础样式，详细样式已移至 table-optimization.css 和 theme-colors.css */
.table th {
    border-top: none;
    /* 字体样式已移至 theme-colors.css 和 table-optimization.css 统一管理 */
}

.table td {
    /* 字体样式已移至 table-optimization.css 统一管理 */
}

/* 表格内所有文本元素使用统一字体 - 由主题文件控制 */
.table td *,
.table th * {
    /* 字体权重由主题文件统一控制 */
}

/* 表格内链接样式 */
.table a {
    font-weight: normal !important;
    color: #007bff;
}

.table a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* 表格内按钮样式 */
.table .btn {
    font-weight: normal !important;
    font-size: 0.8rem !important;
}

/* 表格内徽章样式 */
.table .badge {
    font-weight: normal !important;
    font-size: 0.75rem !important;
}

/* 控制面板样式 */
.dashboard-card {
    transition: transform 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

/* 自定义颜色 */
.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

/* 通知样式 */
.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    transform: translate(25%, -25%);
}

.notification-dropdown {
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    white-space: normal;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: #f8f9fa;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.notification-content {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
    text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }

    .notification-dropdown {
        width: 280px;
    }
}
