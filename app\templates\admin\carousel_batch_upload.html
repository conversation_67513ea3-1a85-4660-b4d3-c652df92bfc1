{% extends "base.html" %}

{% block title %}批量上传轮播图{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>批量上传轮播图
                    </h3>
                </div>

                <form method="POST" enctype="multipart/form-data" id="batchUploadForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="card-body">
                        <div class="row">
                            <!-- 左侧：基本信息 -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="default_description" class="form-label">默认描述</label>
                                    <textarea class="form-control"
                                              id="default_description"
                                              name="default_description"
                                              rows="3"
                                              placeholder="为所有轮播图设置统一的描述信息">{{ request.form.get('default_description', '') }}</textarea>
                                    <div class="form-text">可选，将应用到所有上传的图片</div>
                                </div>

                                <div class="mb-3">
                                    <label for="default_link_url" class="form-label">默认链接</label>
                                    <input type="text"
                                           class="form-control"
                                           id="default_link_url"
                                           name="default_link_url"
                                           value="/"
                                           placeholder="/">
                                    <div class="form-text">
                                        点击图片时跳转的链接<br>
                                        <small class="text-muted">默认跳转到首页 (/)，将应用到所有图片</small>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary set-default-link-btn" data-url="/">
                                            <i class="fas fa-home me-1"></i>首页
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary set-default-link-btn" data-url="/auth/login">
                                            <i class="fas fa-sign-in-alt me-1"></i>登录
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary set-default-link-btn" data-url="/auth/register">
                                            <i class="fas fa-user-plus me-1"></i>注册
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               checked>
                                        <label class="form-check-label" for="is_active">
                                            启用所有轮播图
                                        </label>
                                    </div>
                                    <div class="form-text">禁用的轮播图不会在首页显示</div>
                                </div>
                            </div>

                            <!-- 右侧：文件上传 -->
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="images" class="form-label">
                                        选择轮播图片 <span class="text-danger">*</span>
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="images"
                                           name="images"
                                           accept="image/*"
                                           multiple
                                           required>
                                    <div class="form-text">
                                        <strong>支持格式：</strong>PNG、JPG、JPEG、GIF、WebP<br>
                                        <strong>推荐尺寸：</strong>1920x800像素（系统会自动压缩）<br>
                                        <strong>文件大小：</strong>每张不超过5MB<br>
                                        <strong>自动优化：</strong>上传后会自动压缩为1920x800尺寸，保持宽高比<br>
                                        <strong>可以选择多张图片一次性上传</strong>
                                    </div>
                                </div>

                                <!-- 拖拽上传区域 -->
                                <div class="upload-drop-zone mb-3" id="dropZone">
                                    <div class="text-center py-5">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">拖拽图片到这里</h5>
                                        <p class="text-muted">或点击上方"选择文件"按钮</p>
                                    </div>
                                </div>

                                <!-- 图片预览区域 -->
                                <div id="previewContainer" style="display: none;">
                                    <label class="form-label">图片预览和标题设置</label>
                                    <div id="previewList" class="border rounded p-3 bg-light">
                                        <!-- 动态生成预览图片 -->
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            每张图片都会创建为独立的轮播图，可以为每张图片设置不同的标题
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('homepage_carousel.admin_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2 reset-form-btn">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-1"></i>批量上传
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style nonce="{{ csp_nonce }}">
.upload-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-drop-zone:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-drop-zone.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.preview-item {
    transition: all 0.3s ease;
}

.preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.preview-image {
    max-height: 120px;
    object-fit: cover;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 页面加载时确保默认值
document.addEventListener('DOMContentLoaded', function() {
    const defaultLinkInput = document.getElementById('default_link_url');

    // 如果输入框为空，设置默认值
    if (!defaultLinkInput.value || defaultLinkInput.value.trim() === '') {
        defaultLinkInput.value = '/';
    }

    // 绑定设置默认链接按钮事件
    document.querySelectorAll('.set-default-link-btn').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.dataset.url;
            document.getElementById('default_link_url').value = url;
            document.getElementById('default_link_url').focus(); // 添加视觉反馈
        });
    });

    // 绑定重置按钮事件
    document.querySelector('.reset-form-btn').addEventListener('click', function() {
        if (confirm('确定要重置表单吗？所有未保存的更改将丢失。')) {
            const form = document.getElementById('batchUploadForm');
            form.reset();
            
            // 重置特定字段的默认值和状态
            document.getElementById('default_link_url').value = '/';
            document.getElementById('is_active').checked = true;
            
            // 清空图片预览区域
            const previewList = document.getElementById('previewList');
            if (previewList) {
                previewList.innerHTML = '';
            }
            const previewContainer = document.getElementById('previewContainer');
            if (previewContainer) {
                previewContainer.style.display = 'none';
            }
        }
    });

    // 多图片预览功能 - 保留原有逻辑，但移除对 highlightActiveButton 的依赖
    document.getElementById('images').addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });

    // 拖拽上传功能 - 保留原有逻辑
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('images');
    
    dropZone.addEventListener('click', () => fileInput.click());
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
    
        const files = e.dataTransfer.files;
        fileInput.files = files;
        handleFiles(files);
    });

});

// 定义 handleFiles 函数
function handleFiles(files) {
    const previewContainer = document.getElementById('previewContainer');
    const previewList = document.getElementById('previewList');

    // 清空之前的预览
    previewList.innerHTML = '';

    if (files.length > 0) {
        let validFiles = 0;
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        Array.from(files).forEach((file, index) => {
            // 检查文件类型
            if (!allowedTypes.includes(file.type)) {
                alert(`文件 "${file.name}" 不支持，请选择 PNG、JPG、JPEG、GIF 或 WebP 格式的图片`);
                return;
            }

            // 检查文件大小
            if (file.size > maxSize) {
                alert(`文件 "${file.name}" 大小超过5MB限制`);
                return;
            }

            validFiles++;

            // 创建预览容器
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item mb-3 p-3 border rounded bg-white';

            // 创建图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewItem.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <img src="${e.target.result}" class="img-fluid preview-image" alt="预览 ${index + 1}">
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label small">图片标题 ${index + 1}</label>
                                    <input type="text" class="form-control form-control-sm"
                                           placeholder="轮播图 ${index + 1}"
                                           name="image_titles[]"
                                           value="轮播图 ${index + 1}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label small">文件信息</label>
                                    <p class="mb-0 text-muted small">${file.name}</p>
                                    <p class="mb-0 text-muted small">大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                previewList.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });

        if (validFiles > 0) {
            previewContainer.style.display = 'block';

            // 显示提示信息
            setTimeout(() => {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'alert alert-info mt-3';
                infoDiv.innerHTML = `
                    <i class="fas fa-info-circle"></i>
                    已选择 ${validFiles} 张图片，将创建 ${validFiles} 个轮播图项目
                `;
                previewList.appendChild(infoDiv);
            }, 100);
        } else {
            previewContainer.style.display = 'none';
        }
    } else {
        previewContainer.style.display = 'none';
    }
}

// 移除不再需要的函数或修改其实现
// 原有的 setDefaultLink 函数可以简化为直接设置输入框值
// 原有的 highlightActiveButton 函数由于依赖 onclick 解析，不符合 CSP 且可能导致错误，直接移除或重写

// 简化的 setDefaultLink (如果需要单独调用)
// function setDefaultLink(url) {
//     const input = document.getElementById('default_link_url');
//     input.value = url;
//     input.focus();
// }

// highlightActiveButton 函数已移除或不在此处实现

</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>