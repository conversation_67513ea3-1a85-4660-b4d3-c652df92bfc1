{% extends 'base.html' %}

{% block title %}移动端菜单测试{% endblock %}

{% block styles %}
<style>
.test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.test-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-item {
    padding: 10px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 4px;
    border-start: 4px solid #007bff;
}

.success {
    border-start-color: #28a745;
    background: #d4edda;
}

.info {
    border-start-color: #17a2b8;
    background: #d1ecf1;
}

.mobile-simulator {
    max-width: 375px;
    margin: 20px auto;
    border: 2px solid #333;
    border-radius: 20px;
    padding: 20px 10px;
    background: #000;
}

.mobile-content {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

@d-flex (max-width: 768px) {
    .test-container {
        padding: 10px;
    }
    
    .mobile-simulator {
        border: none;
        background: transparent;
        padding: 0;
        max-width: 100%;
    }
    
    .mobile-content {
        border-radius: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-section">
        <h2><i class="fas fa-mobile-alt"></i> 移动端菜单测试</h2>
        <p class="text-muted">测试移动端下拉菜单的交互功能和食材功能整合效果</p>
    </div>

    <div class="test-section">
        <h3><i class="fas fa-check-circle text-success"></i> 修复内容</h3>
        
        <div class="test-item success">
            <strong>📱 移动端交互修复</strong>
            <ul class="mb-0 mt-2">
                <li>修复了移动端下拉菜单无法点击的问题</li>
                <li>添加了触摸优化和点击反馈</li>
                <li>改善了移动端用户交互体验</li>
                <li>确保移动端菜单项全宽显示</li>
            </ul>
        </div>

        <div class="test-item info">
            <strong>🥕 食材功能整合</strong>
            <ul class="mb-0 mt-2">
                <li>将食材管理整合到菜单规划模块</li>
                <li>食谱与食材功能逻辑分组</li>
                <li>添加了清晰的分组标题</li>
                <li>提高了功能查找效率</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3><i class="fas fa-utensils"></i> 新的菜单规划结构</h3>
        
        <div class="row">
            <div class="col-md-6">
                <h5>📋 菜单计划</h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-calendar-week text-primary"></i> 周菜单计划</li>
                    <li><i class="fas fa-list-alt text-primary"></i> 菜单管理</li>
                    <li><i class="fas fa-sync text-primary"></i> 菜单同步</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>🥕 食谱与食材</h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-book text-success"></i> 食谱库</li>
                    <li><i class="fas fa-carrot text-success"></i> 食材管理</li>
                    <li><i class="fas fa-tags text-success"></i> 食材分类</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3><i class="fas fa-tasks"></i> 测试步骤</h3>
        
        <div class="test-item">
            <strong>步骤 1：桌面端测试</strong>
            <p class="mb-0 mt-2">在桌面浏览器中点击导航栏的"菜单规划"，查看新的分组结构</p>
        </div>

        <div class="test-item">
            <strong>步骤 2：移动端测试</strong>
            <p class="mb-0 mt-2">在移动设备或使用浏览器开发者工具的移动模式测试下拉菜单</p>
        </div>

        <div class="test-item">
            <strong>步骤 3：功能验证</strong>
            <p class="mb-0 mt-2">点击各个菜单项，确认链接正常工作且功能可访问</p>
        </div>
    </div>

    <div class="mobile-simulator d-none d-md-block">
        <div class="mobile-content">
            <div class="text-center p-3 bg-primary text-white">
                <h6 class="mb-0">移动端模拟器</h6>
                <small>在实际移动设备上测试效果更佳</small>
            </div>
            <div class="p-3">
                <p class="text-center text-muted">
                    <i class="fas fa-hand-pointer fa-2x"></i><br>
                    请在移动设备上<br>
                    测试导航菜单功能
                </p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3><i class="fas fa-chart-bar"></i> 优化效果</h3>
        
        <div class="row">
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                    <h5>移动端体验</h5>
                    <p class="text-muted">完美的触摸交互</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-sitemap fa-3x text-success mb-3"></i>
                    <h5>逻辑分组</h5>
                    <p class="text-muted">清晰的功能组织</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-search fa-3x text-info mb-3"></i>
                    <h5>查找效率</h5>
                    <p class="text-muted">快速定位功能</p>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3><i class="fas fa-lightbulb"></i> 使用建议</h3>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> 移动端使用提示</h6>
            <ul class="mb-0">
                <li>在移动设备上，点击导航栏菜单项会展开下拉菜单</li>
                <li>菜单项现在支持触摸操作，响应更加灵敏</li>
                <li>食材相关功能现在集中在"菜单规划"模块中</li>
                <li>使用分组标题快速定位所需功能</li>
            </ul>
        </div>

        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle"></i> 功能整合优势</h6>
            <ul class="mb-0">
                <li>减少了在不同模块间的跳转</li>
                <li>菜单规划相关功能更加集中</li>
                <li>提高了工作流程的连贯性</li>
                <li>新用户更容易理解功能布局</li>
            </ul>
        </div>
    </div>

    <div class="test-section text-center">
        <h3><i class="fas fa-rocket"></i> 测试完成</h3>
        <p class="text-muted">如果您在移动端可以正常点击菜单项，说明修复成功！</p>
        <a href="{{ url_for('main.index') }}" class="btn btn-primary">
            <i class="fas fa-home"></i> 返回首页
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 检测是否为移动设备
    function isMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    // 显示设备类型
    const deviceType = isMobile() ? '移动设备' : '桌面设备';
    console.log('当前设备类型:', deviceType);
    
    // 添加设备类型提示
    const container = document.querySelector('.test-container');
    if (container) {
        const deviceAlert = document.createElement('div');
        deviceAlert.className = 'alert alert-' + (isMobile() ? 'success' : 'warning');
        deviceAlert.innerHTML = `
            <i class="fas fa-${isMobile() ? 'mobile-alt' : 'desktop'}"></i>
            当前检测到${deviceType}，${isMobile() ? '可以直接测试移动端菜单功能' : '建议使用开发者工具切换到移动模式测试'}
        `;
        container.insertBefore(deviceAlert, container.firstChild);
    }
});
</script>
{% endblock %}
