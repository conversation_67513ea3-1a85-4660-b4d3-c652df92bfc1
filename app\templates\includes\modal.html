{% macro confirm_modal(id="confirmModal", title="确认操作", message="您确定要执行此操作吗？", confirm_text="确认", cancel_text="取消", confirm_class="btn-danger") %}
<div class="modal fade" id="{{ id }}" tabindex="-1" role="dialog" aria-labelledby="{{ id }}Label" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        {{ message }}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ cancel_text }}</button>
        <button type="button" class="btn {{ confirm_class }} confirm-action">{{ confirm_text }}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

{% macro form_modal(id="formModal", title="表单", submit_text="提交", cancel_text="取消", size="") %}
<div class="modal fade" id="{{ id }}" tabindex="-1" role="dialog" aria-labelledby="{{ id }}Label" aria-hidden="true">
  <div class="modal-dialog {{ size }}" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        {{ caller() }}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ cancel_text }}</button>
        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

{% macro alert_modal(id="alertModal", title="提示", ok_text="确定") %}
<div class="modal fade" id="{{ id }}" tabindex="-1" role="dialog" aria-labelledby="{{ id }}Label" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        {{ caller() }}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">{{ ok_text }}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}
