{% extends 'admin/system/base.html' %}

{% block title %}模块可见性管理 - {{ super() }}{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>模块可见性管理</h2>
            <p class="text-muted">
                在此页面，您可以控制不同角色可以看到哪些导航菜单模块。
                <br>
                <strong>注意：</strong> 这只会影响菜单的显示，不会影响实际的功能权限。用户仍然可以通过直接访问URL来使用功能。
            </p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-primary" id="expandAll">
                <i class="fas fa-expand-alt"></i> 展开全部
            </button>
            <button type="button" class="btn btn-outline-secondary" id="collapseAll">
                <i class="fas fa-compress-alt"></i> 折叠全部
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">角色模块可见性设置</h5>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs" id="roleTabs" role="tablist">
                {% for role in roles %}
                <li class="nav-item" role="presentation">
                    <a class="nav-link {% if loop.first %}active{% endif %}"
                       id="role-{{ role.id }}-tab"
                       data-bs-toggle="tab"
                       href="#role-{{ role.id }}"
                       role="tab"
                       aria-controls="role-{{ role.id }}"
                       aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                        {{ role.name }}
                    </a>
                </li>
                {% endfor %}
            </ul>
            <div class="tab-content mt-3" id="roleTabsContent">
                {% for role in roles %}
                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                     id="role-{{ role.id }}"
                     role="tabpanel"
                     aria-labelledby="role-{{ role.id }}-tab">

                    <div class="mb-3">
                        <button type="button" class="btn btn-success btn-sm set-all-visible" data-role-id="{{ role.id }}">
                            <i class="fas fa-eye"></i> 全部可见
                        </button>
                        <button type="button" class="btn btn-danger btn-sm set-all-hidden" data-role-id="{{ role.id }}">
                            <i class="fas fa-eye-slash"></i> 全部隐藏
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th class="w-30">主模块</th>
                                    <th class="w-60">子模块</th>
                                    <th style="width: 10%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for module in modules %}
                                <tr class="module-row" data-module-id="{{ module.id }}">
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox"
                                                   class="custom-control-input module-visibility-toggle"
                                                   id="module-{{ role.id }}-{{ module.id }}"
                                                   data-role-id="{{ role.id }}"
                                                   data-module-id="{{ module.id }}"
                                                   {% if visibilities.get(role.id, {}).get(module.id, True) %}checked{% endif %}>
                                            <label class="form-check-label" for="module-{{ role.id }}-{{ module.id }}">
                                                <i class="{{ module.get('icon', 'fas fa-folder') }}"></i> {{ module.name }}
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        {% if module.children %}
                                        <div class="row">
                                            {% for child in module.children %}
                                            <div class="col-md-4 mb-2">
                                                <div class="custom-control custom-switch">
                                                    <input type="checkbox"
                                                           class="custom-control-input submodule-visibility-toggle"
                                                           id="module-{{ role.id }}-{{ child.id }}"
                                                           data-role-id="{{ role.id }}"
                                                           data-module-id="{{ child.id }}"
                                                           data-parent-id="{{ module.id }}"
                                                           {% if visibilities.get(role.id, {}).get(child.id, True) %}checked{% endif %}>
                                                    <label class="form-check-label" for="module-{{ role.id }}-{{ child.id }}">
                                                        {{ child.name }}
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">无子模块</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary toggle-children" data-role-id="{{ role.id }}" data-module-id="{{ module.id }}">
                                                <i class="fas fa-sync-alt"></i> 切换子模块
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 获取CSRF令牌
        var csrfToken = "{{ csrf_token() }}";

        // 设置AJAX默认头部包含CSRF令牌
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        });

        // 打印CSRF令牌，用于调试
        console.log("CSRF Token:", csrfToken);

        // 切换模块可见性
        $('.module-visibility-toggle, .submodule-visibility-toggle').change(function() {
            const roleId = $(this).data('role-id');
            const moduleId = $(this).data('module-id');
            const isVisible = $(this).prop('checked');

            // 发送AJAX请求更新可见性
            $.ajax({
                url: '{{ url_for("system.update_module_visibility") }}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    role_id: roleId,
                    module_id: moduleId,
                    is_visible: isVisible
                }),
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    toastr.error('更新失败: ' + xhr.responseJSON?.message || '未知错误');
                }
            });
        });

        // 切换子模块可见性
        $('.toggle-children').click(function() {
            const roleId = $(this).data('role-id');
            const moduleId = $(this).data('module-id');
            const row = $(this).closest('tr');
            const parentCheckbox = row.find('.module-visibility-toggle');
            const childCheckboxes = row.find('.submodule-visibility-toggle');

            // 获取父模块的状态
            const isVisible = parentCheckbox.prop('checked');

            // 设置所有子模块的状态与父模块相同
            childCheckboxes.prop('checked', isVisible);

            // 收集所有子模块ID
            const moduleIds = [];
            childCheckboxes.each(function() {
                moduleIds.push($(this).data('module-id'));
            });

            // 发送AJAX请求批量更新可见性
            if (moduleIds.length > 0) {
                $.ajax({
                    url: '{{ url_for("system.bulk_update_module_visibility") }}',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        role_id: roleId,
                        module_ids: moduleIds,
                        is_visible: isVisible
                    }),
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        toastr.error('更新失败: ' + xhr.responseJSON?.message || '未知错误');
                    }
                });
            }
        });

        // 设置全部可见
        $('.set-all-visible').click(function() {
            const roleId = $(this).data('role-id');
            const tab = $('#role-' + roleId);
            const checkboxes = tab.find('.module-visibility-toggle, .submodule-visibility-toggle');

            // 设置所有复选框为选中状态
            checkboxes.prop('checked', true);

            // 收集所有模块ID
            const moduleIds = [];
            checkboxes.each(function() {
                moduleIds.push($(this).data('module-id'));
            });

            // 发送AJAX请求批量更新可见性
            if (moduleIds.length > 0) {
                $.ajax({
                    url: '{{ url_for("system.bulk_update_module_visibility") }}',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        role_id: roleId,
                        module_ids: moduleIds,
                        is_visible: true
                    }),
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        toastr.error('更新失败: ' + xhr.responseJSON?.message || '未知错误');
                    }
                });
            }
        });

        // 设置全部隐藏
        $('.set-all-hidden').click(function() {
            const roleId = $(this).data('role-id');
            const tab = $('#role-' + roleId);
            const checkboxes = tab.find('.module-visibility-toggle, .submodule-visibility-toggle');

            // 设置所有复选框为未选中状态
            checkboxes.prop('checked', false);

            // 收集所有模块ID
            const moduleIds = [];
            checkboxes.each(function() {
                moduleIds.push($(this).data('module-id'));
            });

            // 发送AJAX请求批量更新可见性
            if (moduleIds.length > 0) {
                $.ajax({
                    url: '{{ url_for("system.bulk_update_module_visibility") }}',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        role_id: roleId,
                        module_ids: moduleIds,
                        is_visible: false
                    }),
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        toastr.error('更新失败: ' + xhr.responseJSON?.message || '未知错误');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
