<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="{{ theme_color|default('primary') }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap5.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=2.4.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-optimization.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-optimization.css') }}?v=1.5.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-image-uploader.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-upload-fix.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inventory-optimization.css') }}?v=1.0.0">

    <!-- 自定义 CSP 修复样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-csp-fixes.css') }}?v=1.0.0">
    <!-- 优雅导航样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/elegant-navigation.css') }}?v=1.0.0">
    <!-- 左右布局样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar-layout.css') }}?v=1.0.0">
    <!-- 布局修复样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout-fix.css') }}?v=1.0.0">
    <!-- 移动端优化样式 - 最后加载确保优先级 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-optimization.css') }}?v=1.1.0">
    {% block styles %}{% endblock %}
    <style>
        /* 导航栏LOGO样式 */
        .navbar-logo {
            transition: all 0.3s ease;
        }
        .navbar-logo:hover {
            transform: scale(1.05);
        }
        .navbar-brand-text {
            white-space: nowrap;
        }

        .navbar-brand-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            line-height: 1.2;
        }

        .navbar-school-name {
            font-size: 0.95rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.95);
            white-space: nowrap;
            margin-top: 6px;
            letter-spacing: 0.3px;
            padding: 2px 8px;
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 移动端适配 */
        @d-flex (max-width: 768px) {
            .navbar-logo {
                height: 32px !important;
                max-width: 100px !important;
            }
            .navbar-brand-text {
                font-size: 0.9rem;
            }
            .navbar-school-name {
                font-size: 0.75rem;
            }
        }

        /* 超小屏幕只显示LOGO */
        @d-flex (max-width: 480px) {
            .navbar-brand-text {
                display: none;
            }
            .navbar-school-name {
                display: none;
            }
            .navbar-logo {
                margin-right: 0 !important;
            }
        }
    </style>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/critical-handler-simple.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</head>
<body data-theme="{{ theme_color|default('primary') }}">
    <!-- 顶部工具栏 -->
    <div class="top-toolbar bg-{{ theme_color|default('primary') }}">
        <div class="toolbar-left">
            <!-- 移动端菜单切换按钮 -->
            <button class="sidebar-toggle d-md-none" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <!-- 品牌标识 -->
            <a class="brand-text" href="{{ url_for('main.index') }}">
                {% if system_logo %}
                <img src="{{ system_logo }}" alt="{{ project_name }}" class="brand-logo me-2">
                {% endif %}
                {{ project_name|default('校园餐智慧食堂平台') }}
            </a>

            {% if current_user.is_authenticated and current_user.get_current_area() %}
            <span class="text-light small">{{ current_user.get_current_area().name }}</span>
            {% endif %}
        </div>

        <div class="toolbar-right">
            <!-- 主题切换器 -->
            <div class="dropdown">
                <button class="toolbar-btn dropdown-toggle" type="button" id="themeDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="切换主题 (Ctrl+Alt+T)">
                    <i class="fas fa-palette"></i>
                </button>
                <div class="dropdown-menu theme-switcher-panel" style="max-height: 400px; overflow-y: auto; min-width: 280px; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.2); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.1);">
                    <h6 class="dropdown-header">🎨 现代专业系列</h6>
                    <a class="dropdown-item theme-option" href="#" data-theme="primary">
                        <span class="theme-preview primary"></span>🌊 海洋蓝主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="secondary">
                        <span class="theme-preview secondary"></span>🔘 现代灰主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="success">
                        <span class="theme-preview success"></span>🌿 自然绿主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="warning">
                        <span class="theme-preview warning"></span>🔥 活力橙主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="info">
                        <span class="theme-preview info"></span>💜 优雅紫主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="danger">
                        <span class="theme-preview danger"></span>❤️ 深邃红主题
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <div class="dropdown-divider"></div>
                    <h6 class="dropdown-header">✨ 经典优雅系列</h6>
                    <a class="dropdown-item theme-option" href="#" data-theme="classic-neutral">
                        <span class="theme-preview classic-neutral"></span>🏛️ 经典中性风
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="modern-neutral">
                        <span class="theme-preview modern-neutral"></span>🏢 现代中性风
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="noble-elegant">
                        <span class="theme-preview noble-elegant"></span>👑 贵族典雅风
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="royal-solemn">
                        <span class="theme-preview royal-solemn"></span>🎭 皇室庄重风
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <div class="dropdown-divider"></div>
                    <h6 class="dropdown-header">🚀 DeepSeek 现代系列</h6>
                    <a class="dropdown-item theme-option" href="#" data-theme="deep-sea-tech">
                        <span class="theme-preview deep-sea-tech"></span>🌊 深海科技蓝
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="soft-morandi">
                        <span class="theme-preview soft-morandi"></span>🎨 柔光莫兰迪
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="minimal-dawn">
                        <span class="theme-preview minimal-dawn"></span>🌅 极简晨曦
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="dark-neon">
                        <span class="theme-preview dark-neon"></span>🌃 暗夜霓虹
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <a class="dropdown-item theme-option" href="#" data-theme="nature-eco">
                        <span class="theme-preview nature-eco"></span>🌿 自然生态绿
                        <i class="fas fa-star theme-favorite" title="收藏主题"></i>
                    </a>
                    <div class="dropdown-divider"></div>
                    <div class="theme-controls px-3 py-2">
                        <small class="text-muted">主题设置</small>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span style="font-size: 0.85rem;">预览模式</span>
                            <button class="btn btn-sm btn-outline-primary" id="themePreviewToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span style="font-size: 0.85rem;">自动切换</span>
                            <button class="btn btn-sm btn-outline-secondary" id="autoThemeToggle">
                                <i class="fas fa-clock"></i>
                            </button>
                        </div>
                        <div class="dropdown-divider mt-3"></div>
                        <div class="d-flex gap-2 mt-2">
                            <button class="btn btn-sm btn-outline-info flex-fill" id="themeStatsBtn" title="主题统计">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success flex-fill" id="themeExportBtn" title="导出设置">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning flex-fill" id="themeImportBtn" title="导入设置">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                        <input type="file" id="themeImportFile" accept=".json" style="display: none;">
                    </div>
                </div>
            </div>

            {% if current_user.is_authenticated %}
            <!-- 通知图标 -->
            <div class="dropdown">
                <button class="toolbar-btn position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-bell"></i>
                    {% if current_user.unread_notifications_count > 0 %}
                    <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill">{{ current_user.unread_notifications_count }}</span>
                    {% endif %}
                </button>
                <div class="dropdown-menu notification-dropdown" style="width: 320px; max-height: 400px; overflow-y: auto;">
                    <h6 class="dropdown-header">通知中心</h6>
                    <div id="notification-list">
                        {% if current_user.recent_notifications %}
                            {% for notification in current_user.recent_notifications %}
                            <a class="dropdown-item notification-item {% if not notification['is_read'] %}unread{% endif %}" href="{{ url_for('notification.view', id=notification['id']) }}">
                                <div class="notification-title">
                                    {% if notification['level'] == 2 %}
                                    <span class="badge bg-danger">紧急</span>
                                    {% elif notification['level'] == 1 %}
                                    <span class="badge bg-warning">重要</span>
                                    {% endif %}
                                    {{ notification['title'] }}
                                </div>
                                <div class="notification-content">{{ notification['content']|truncate(50) }}</div>
                                <div class="notification-time">
                                    {% if notification['created_at'] is string %}
                                        {{ notification['created_at'] }}
                                    {% else %}
                                        {{ notification['created_at']|format_datetime }}
                                    {% endif %}
                                </div>
                            </a>
                            {% endfor %}
                        {% else %}
                        <div class="dropdown-item text-center">暂无通知</div>
                        {% endif %}
                    </div>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item text-center" href="{{ url_for('notification.index') }}">查看全部通知</a>
                    {% if current_user.unread_notifications_count > 0 %}
                    <a class="dropdown-item text-center" href="{{ url_for('notification.mark_all_read') }}">全部标为已读</a>
                    {% endif %}
                </div>
            </div>

            <!-- 用户菜单 -->
            <div class="dropdown">
                <button class="toolbar-btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user"></i>
                    {{ current_user.username }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#">
                        <i class="fas fa-user"></i> 个人资料
                    </a>
                    <a class="dropdown-item" href="{{ url_for('help.index') }}">
                        <i class="fas fa-question-circle"></i> 帮助中心
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </a>
                </div>
            </div>
            {% else %}
            <a class="toolbar-btn" href="{{ url_for('auth.login') }}">登录</a>
            <a class="toolbar-btn" href="{{ url_for('auth.register') }}">注册</a>
            {% endif %}
        </div>
    </div>

    <!-- 主要布局容器 -->
    <div class="app-wrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <ul class="sidebar-nav">
                {% if current_user.is_authenticated %}
                    {% for menu_item in user_menu %}
                        {% if menu_item.children %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                </a>
                                <div class="dropdown-menu">
                                    {% for child in menu_item.children %}
                                        {% if child.get('is_header') %}
                                            <h6 class="dropdown-header">{{ child.name }}</h6>
                                        {% else %}
                                            <a class="dropdown-item" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}">
                                                {% if child.icon %}<i class="{{ child.icon }}"></i>{% endif %}
                                                {{ child.name }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                    {% if menu_item.id == 'area' and current_user.area_id %}
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="{{ url_for('area.view_area', id=current_user.area_id) }}">
                                            当前区域: {{ current_user_area_name or '未知区域' }}
                                        </a>
                                    {% endif %}
                                </div>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>

        <!-- 移动端遮罩层 -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- 底部 -->
    <footer class="footer">
        <span class="text-muted">{{ project_name|default('校园餐智慧食堂(Scmmp) ') }} &copy; {{ now.year }}</span>
    </footer>

    <!-- 首先加载 jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>

    <!-- 事件处理器管理器（必须在其他事件处理器之前加载） -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/event-handler-manager.js') }}"></script>

    <!-- 其他脚本 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/csp-helper.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/jquery-ui-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/datepicker-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/datatables-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/form-validation-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/i18n.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mock-api-handler.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/main.js') }}?v=1.0.2"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/theme-switcher.js') }}?v=1.0.3"></script>
    <!-- 移动端优化后的主题功能 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/advanced-theme-features.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/auth-helper.js') }}?v=1.0.1"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/enhanced-image-uploader.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.2"></script>
    <!-- 移动端表格转卡片工具 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-table-cards.js') }}?v=1.0.0"></script>
    <!-- 移动端增强功能 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-enhancements.js') }}?v=2.0.0"></script>
    <!-- 左右布局功能脚本 -->
    <script nonce="{{ csp_nonce }}">
        $(document).ready(function() {
            // 侧边栏切换功能
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');

                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('show');
                }
            }

            // 绑定侧边栏切换按钮
            $('#sidebarToggle').on('click', toggleSidebar);

            // 点击遮罩层关闭侧边栏
            $('#sidebarOverlay').on('click', function() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });

            // 侧边栏下拉菜单功能
            $('.sidebar-nav .dropdown-toggle').on('click', function(e) {
                e.preventDefault();
                const $dropdown = $(this).closest('.dropdown');
                const $menu = $dropdown.find('.dropdown-menu');

                // 关闭其他下拉菜单
                $('.sidebar-nav .dropdown').not($dropdown).removeClass('show');
                $('.sidebar-nav .dropdown-menu').not($menu).hide();

                // 切换当前下拉菜单
                $dropdown.toggleClass('show');
                $menu.toggle();
            });

            // 点击菜单项后在移动端关闭侧边栏
            $('.sidebar-nav .dropdown-item, .sidebar-nav .nav-link:not(.dropdown-toggle)').on('click', function() {
                if (window.innerWidth <= 768) {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            });

            // 窗口大小改变时的处理
            $(window).on('resize', function() {
                if (window.innerWidth > 768) {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            });

            // 设置当前页面的活动状态
            const currentPath = window.location.pathname;
            $('.sidebar-nav .nav-link').each(function() {
                const href = $(this).attr('href');
                if (href && currentPath.includes(href) && href !== '/') {
                    $(this).addClass('active');
                }
            });
        });
    </script>

    <!-- 临时递归修复脚本 -->
    <script nonce="{{ csp_nonce }}">
        // 检测到递归错误时自动修复
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error('检测到递归错误，正在自动修复...');

                // 加载快速修复脚本
                const script = document.createElement('script');
                script.src = '/static/js/quick_fix_recursion.js?v=' + Date.now();
                script.onload = function() {
                    console.log('递归修复脚本已加载');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script nonce="{{ csp_nonce }}">
        // 配置toastr通知
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // 简单的主题测试函数
        window.testTheme = function(theme) {
            if (window.switchTheme) {
                window.switchTheme(theme);
                console.log('主题已切换到:', theme);
            }
        };

        // 初始化本地化设置
        $(document).ready(function() {
            // 设置 moment.js 的语言
            moment.locale('zh-CN');

            // 设置 bootstrap-table 的默认选项
            $.extend($.fn.bootstrapTable.defaults, {
                locale: 'zh-CN',
                formatLoadingMessage: function() {
                    return '正在加载中...';
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
