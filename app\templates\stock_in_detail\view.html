{% extends 'base.html' %}

{% block title %}入库食材详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">入库食材详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_in.view', id=item.stock_in_id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回入库单
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 食材基本信息 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">食材基本信息</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>食材名称</label>
                                                <p class="form-control-static">{{ item.ingredient_name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>食材分类</label>
                                                <p class="form-control-static">{{ item.ingredient_category }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>批次号</label>
                                                <p class="form-control-static">{{ batch_number }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>入库数量</label>
                                                <p class="form-control-static">{{ item.quantity }} {{ item.unit }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>生产日期</label>
                                                <p class="form-control-static">{{ item.production_date|format_datetime('%Y-%m-%d') }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>过期日期</label>
                                                <p class="form-control-static">{{ item.expiry_date|format_datetime('%Y-%m-%d') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>质量状态</label>
                                                <p class="form-control-static">{{ item.quality_status or '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>单价</label>
                                                <p class="form-control-static">{{ item.unit_price or '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 入库信息 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">入库信息</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>入库单号</label>
                                                <p class="form-control-static">
                                                    <a href="{{ url_for('stock_in.view', id=item.stock_in_id) }}">{{ item.stock_in_number }}</a>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>入库日期</label>
                                                <p class="form-control-static">{{ item.stock_in_date|format_datetime('%Y-%m-%d') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>入库类型</label>
                                                <p class="form-control-static">{{ item.stock_in_type }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>仓库</label>
                                                <p class="form-control-static">{{ item.warehouse_name }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>存储位置</label>
                                                <p class="form-control-static">{{ item.storage_location_name }} ({{ item.location_code }})</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>操作人</label>
                                                <p class="form-control-static">{{ item.operator_name }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label>备注</label>
                                                <p class="form-control-static">{{ item.notes or '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 供应商信息 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-info">
                                <div class="card-header">
                                    <h3 class="card-title">供应商信息</h3>
                                </div>
                                <div class="card-body">
                                    {% if item.supplier_name %}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>供应商名称</label>
                                                <p class="form-control-static">{{ item.supplier_name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>法人代表</label>
                                                <p class="form-control-static">{{ item.legal_representative or '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>联系电话</label>
                                                <p class="form-control-static">{{ item.phone or '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label>地址</label>
                                                <p class="form-control-static">{{ item.address or '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-warning">
                                        未关联供应商信息
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 检验检疫证明 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-warning">
                                <div class="card-header">
                                    <h3 class="card-title">检验检疫证明</h3>
                                </div>
                                <div class="card-body">
                                    {% if inspection_certificates %}
                                    <div class="row">
                                        {% for cert in inspection_certificates %}
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">{{ cert.document_type }}</h5>
                                                    <p class="card-text">证书编号: {{ cert.certificate_number or '-' }}</p>
                                                    <p class="card-text">发证日期: {{ cert.issue_date|format_datetime('%Y-%m-%d') }}</p>
                                                    <a href="{{ url_for('static', filename=cert.file_path) }}" class="btn btn-primary" target="_blank">查看证书</a>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="alert alert-warning">
                                        暂无检验检疫证明
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 关联消耗计划 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-danger">
                                <div class="card-header">
                                    <h3 class="card-title">关联消耗计划</h3>
                                </div>
                                <div class="card-body">
                                    {% if consumption_plans %}
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>消耗计划ID</th>
                                                    <th>消耗日期</th>
                                                    <th>餐次</th>
                                                    <th>用餐人数</th>
                                                    <th>区域</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for plan in consumption_plans %}
                                                <tr>
                                                    <td>{{ plan.id }}</td>
                                                    <td>{{ plan.consumption_date|format_datetime('%Y-%m-%d') }}</td>
                                                    <td>{{ plan.meal_type }}</td>
                                                    <td>{{ plan.diners_count }}</td>
                                                    <td>{{ plan.area_name }}</td>
                                                    <td>
                                                        <a href="{{ url_for('consumption_plan.view', id=plan.id) }}" class="btn btn-info btn-sm">
                                                            <i class="fas fa-eye"></i> 查看
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-warning">
                                        暂无关联消耗计划
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
