{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .context-guidance {
        border-start: 4px solid #17a2b8;
        background-color: #f8f9fa;
    }
    .workflow-context {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }
    .previous-step, .current-step, .next-step {
        flex: 1;
        padding: 0 10px;
    }
    .current-step {
        border-start: 1px solid #dee2e6;
        border-end: 1px solid #dee2e6;
    }
    .document-upload-box {
        background-color: #f8f9fa;
        transition: all 0.3s;
    }
    .document-upload-box:hover {
        background-color: #e9ecef;
    }
    .storage-category {
        margin-bottom: 15px;
    }
    .guidance-tips li {
        margin-bottom: 5px;
    }
    .step-guide-card {
        margin-bottom: 20px;
    }
    .alert-icon {
        font-size: 1.2rem;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 入库流程上下文引导框 -->
    <div class="context-guidance card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-dolly-flatbed"></i> 食材入库管理 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle alert-icon"></i> <strong>重要提示：</strong> 入库是食品安全管理的关键环节，请确保所有必要文件和检查都已完成。
            </div>

            <div class="workflow-context mt-3">
                <div class="previous-step">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-clipboard-check"></i> 入库检查</p>
                    <small>确保食材已通过质量检查</small>
                    <div class="mt-2">
                        <a href="{{ url_for('inspection.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回检查
                        </a>
                    </div>
                </div>
                <div class="current-step bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="fw-bold"><i class="fas fa-dolly-flatbed"></i> 食材入库</p>
                    <small>记录入库信息并更新库存</small>
                </div>
                <div class="next-step">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-chart-line"></i> 消耗量计划</p>
                    <small>根据菜单计划食材消耗</small>
                    <div class="mt-2">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往下一步
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库前置检查清单 -->
    <div class="card mb-4 border-danger">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0 d-flex justify-content-between align-items-center">
                <span><i class="fas fa-tasks"></i> 入库前置检查清单</span>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#preCheckList">
                    展开/收起
                </button>
            </h5>
        </div>
        <div class="collapse show" id="preCheckList">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check1" required>
                                <label class="form-check-label" for="check1"><strong>食材质量验收</strong> - 确认食材外观、气味、质地符合标准</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check2" required>
                                <label class="form-check-label" for="check2"><strong>供应商供货单据</strong> - 已核对并保存供应商送货单</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check3" required>
                                <label class="form-check-label" for="check3"><strong>食材检疫证明</strong> - 已验证肉类、水产品等检疫证明</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check4" required>
                                <label class="form-check-label" for="check4"><strong>食材温度检查</strong> - 冷藏/冷冻食材温度符合要求</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check5" required>
                                <label class="form-check-label" for="check5"><strong>包装完整性</strong> - 确认包装无破损、无污染</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check6" required>
                                <label class="form-check-label" for="check6"><strong>保质期检查</strong> - 确认所有食材在保质期内</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info mt-2 mb-0">
                    <small><i class="fas fa-info-circle"></i> 提示：所有检查项必须通过才能进行入库操作。如有不符合项，请退回相关食材并联系供应商。</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 必要文档上传提示 -->
    <div class="card mb-4 border-warning">
        <div class="card-header bg-warning">
            <h5 class="mb-0 d-flex justify-content-between align-items-center">
                <span><i class="fas fa-file-upload"></i> 必要文档上传</span>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#docUpload">
                    展开/收起
                </button>
            </h5>
        </div>
        <div class="collapse" id="docUpload">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="document-upload-box p-3 border rounded mb-3">
                            <h6><i class="fas fa-file-invoice"></i> 供应商送货单</h6>
                            <p class="small text-muted">上传供应商提供的送货单据，确保数量与实际相符</p>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="deliveryNote">
                                <label class="custom-file-label" for="deliveryNote">选择文件</label>
                            </div>
                            <div class="document-status mt-2">
                                <span class="badge badge-danger">未上传</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="document-upload-box p-3 border rounded mb-3">
                            <h6><i class="fas fa-certificate"></i> 食材检疫证明</h6>
                            <p class="small text-muted">上传肉类、水产品等需要检疫证明的文档</p>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="quarantineCert">
                                <label class="custom-file-label" for="quarantineCert">选择文件</label>
                            </div>
                            <div class="document-status mt-2">
                                <span class="badge badge-danger">未上传</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="document-upload-box p-3 border rounded mb-3">
                            <h6><i class="fas fa-clipboard-check"></i> 验收记录表</h6>
                            <p class="small text-muted">上传食材验收记录表，记录验收人员和验收结果</p>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="inspectionRecord">
                                <label class="custom-file-label" for="inspectionRecord">选择文件</label>
                            </div>
                            <div class="document-status mt-2">
                                <span class="badge badge-danger">未上传</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-warning mt-2 mb-0">
                    <i class="fas fa-exclamation-triangle"></i> <strong>重要提示：</strong> 根据食品安全管理规定，以上文档必须妥善保存，以便追溯。建议使用扫描件或清晰照片上传。
                </div>
            </div>
        </div>
    </div>

    <!-- 入库数据录入指引 -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0 d-flex justify-content-between align-items-center">
                <span><i class="fas fa-edit"></i> 入库数据录入指引</span>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#dataEntryGuide">
                    展开/收起
                </button>
            </h5>
        </div>
        <div class="collapse" id="dataEntryGuide">
            <div class="card-body">
                <div class="alert alert-light border">
                    <h6 class="fw-bold">数据录入注意事项：</h6>
                    <ul class="mb-0">
                        <li><strong>数量准确性</strong> - 确保入库数量与实际收到数量一致，避免库存不准确</li>
                        <li><strong>单位统一</strong> - 使用系统规定的标准单位（千克/克/个/箱等），避免单位混乱</li>
                        <li><strong>价格记录</strong> - 准确记录单价和总价，作为成本核算依据</li>
                        <li><strong>批次管理</strong> - 记录生产批次号，便于追溯管理</li>
                        <li><strong>保质期</strong> - 记录生产日期和保质期，系统将自动计算到期日</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库表单 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 创建方式选择 -->
                    {% if not stock_in %}
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">入库单创建提示</h5>
                                <p>您可以选择以下两种方式创建入库单：</p>
                                <ol>
                                    <li><strong>直接创建</strong> - 手动填写入库单信息，然后在编辑页面添加入库明细</li>
                                    <li><strong>从采购订单创建</strong> - 选择一个采购订单，系统将自动导入采购订单中的食材明细</li>
                                </ol>
                                <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle"></i> 注意：创建入库单后，您将被引导至编辑页面，在那里您可以添加或修改入库明细。</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-edit"></i> 直接创建入库单</h5>
                                </div>
                                <div class="card-body">
                                    <p>手动填写入库单基本信息，然后在编辑页面添加入库明细。</p>
                                    <button type="button" class="btn btn-primary w-100" id="directCreateBtn" data-onclick="showDirectCreateForm()">
                                        <i class="fas fa-plus-circle"></i> 直接创建入库单
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-file-import"></i> 从采购订单创建</h5>
                                </div>
                                <div class="card-body">
                                    <p>选择一个采购订单，系统将自动导入采购订单中的食材明细。</p>
                                    <button type="button" class="btn btn-success w-100" id="createFromPurchaseBtn" data-onclick="showPurchaseOrderList()">
                                        <i class="fas fa-file-import"></i> 从采购订单创建
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 采购订单列表 -->
                    <div id="purchaseOrderListContainer" style="display: none;" class="mb-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-list"></i> 选择采购订单</h5>
                                <div>
                                    <input type="text" id="orderSearchInput" class="form-control form-control-sm" placeholder="搜索订单编号或供应商..." style="width: 250px;">
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary active" data-filter="all">全部</button>
                                        <button type="button" class="btn btn-outline-warning" data-filter="待确认">待确认</button>
                                        <button type="button" class="btn btn-outline-success" data-filter="已确认">已确认</button>
                                    </div>
                                    <div class="btn-group btn-group-sm ms-2" role="group">
                                        <button type="button" class="btn btn-outline-secondary" id="sortByDate">按日期排序 <i class="fas fa-sort"></i></button>
                                        <button type="button" class="btn btn-outline-secondary" id="sortBySupplier">按供应商排序 <i class="fas fa-sort"></i></button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="purchaseOrderTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th width="15%">订单编号</th>
                                                <th width="20%">供应商</th>
                                                <th width="15%">订单日期</th>
                                                <th width="10%">状态</th>
                                                <th width="15%">总金额</th>
                                                <th width="25%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for order in purchase_orders %}
                                            <tr class="order-row" data-status="{{ order.status }}" data-order-id="{{ order.id }}">
                                                <td>
                                                    <strong>{{ order.order_number }}</strong>
                                                    <div class="small text-muted">{{ order.items|length }}种食材</div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-building text-secondary me-2"></i>
                                                        {{ order.supplier.name if order.supplier else '自购' }}
                                                    </div>
                                                </td>
                                                <td>{{ order.order_date|format_datetime('%Y-%m-%d') }}</td>
                                                <td>
                                                    <span class="badge badge-{{ 'success' if order.status == '已确认' else 'warning' }} badge-pill px-3 py-2">
                                                        {{ order.status }}
                                                    </span>
                                                </td>
                                                <td class="text-end">
                                                    <strong>¥{{ "%.2f"|format(order.total_amount|float) }}</strong>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-info btn-sm preview-btn" data-id="{{ order.id }}">
                                                            <i class="fas fa-eye"></i> 预览
                                                        </button>
                                                        <a href="{{ url_for('stock_in.create_from_purchase', purchase_order_id=order.id) }}" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-check"></i> 选择
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="6" class="text-center">暂无可用的采购订单</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <button type="button" class="btn btn-secondary mt-3" data-onclick="hidePurchaseOrderList()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 直接创建表单 -->
                    <div id="directCreateFormContainer" {% if stock_in %}style="display: block;"{% else %}style="display: none;"{% endif %}>
                        <form method="post" action="{{ url_for('stock_in.edit', id=stock_in.id) if stock_in else url_for('stock_in.create') }}" novalidate novalidate>
                            <div class="row">
                                <div class="col-md-6">
                                    {% if not stock_in %}
                                    <div class="mb-3">
                                        <label for="warehouse_id">仓库 <span class="text-danger">*</span></label>
                                        <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                            <option value="">-- 请选择仓库 --</option>
                                            {% for warehouse in warehouses %}
                                            <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    {% endif %}

                                    <div class="mb-3">
                                        <label for="stock_in_date">入库日期 <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="stock_in_date" name="stock_in_date" value="{{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in else '' }}" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="stock_in_type">入库类型 <span class="text-danger">*</span></label>
                                        <select class="form-control" id="stock_in_type" name="stock_in_type" required>
                                            <option value="">-- 请选择入库类型 --</option>
                                            <option value="采购入库" {% if stock_in and stock_in.stock_in_type == '采购入库' %}selected{% endif %}>采购入库</option>
                                            <option value="调拨入库" {% if stock_in and stock_in.stock_in_type == '调拨入库' %}selected{% endif %}>调拨入库</option>
                                            <option value="退货入库" {% if stock_in and stock_in.stock_in_type == '退货入库' %}selected{% endif %}>退货入库</option>
                                            <option value="其他入库" {% if stock_in and stock_in.stock_in_type == '其他入库' %}selected{% endif %}>其他入库</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="supplier_id">供应商</label>
                                        <select class="form-control" id="supplier_id" name="supplier_id">
                                            <option value="">-- 请选择供应商 --</option>
                                            {% for supplier in suppliers %}
                                            <option value="{{ supplier.id }}" {% if stock_in and stock_in.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes">备注</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ stock_in.notes if stock_in else '' }}</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> <strong>重要提示：</strong> 创建入库单后，您将被引导至编辑页面，在那里您可以添加或修改入库明细。
                            </div>

                            <div class="row">
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary">保存并继续</button>
                                    <a href="{{ url_for('stock_in.index') }}" class="btn btn-default">取消</a>
                                </div>
                            </div>

                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 食材存放指导 -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0 d-flex justify-content-between align-items-center">
                <span><i class="fas fa-warehouse"></i> 食材存放指导</span>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#storageGuide">
                    展开/收起
                </button>
            </h5>
        </div>
        <div class="collapse" id="storageGuide">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="storage-category p-3 border rounded">
                            <h6 class="text-danger"><i class="fas fa-drumstick-bite"></i> 肉类/水产品</h6>
                            <ul class="small ps-3 mb-0">
                                <li>冷冻肉类：-18℃以下冷冻</li>
                                <li>冷鲜肉类：0-4℃冷藏</li>
                                <li>鱼类：独立包装，避免交叉污染</li>
                                <li>贝类：0-4℃冷藏，使用专用容器</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="storage-category p-3 border rounded">
                            <h6 class="text-success"><i class="fas fa-carrot"></i> 蔬菜/水果</h6>
                            <ul class="small ps-3 mb-0">
                                <li>叶菜类：7-10℃冷藏，保持适当湿度</li>
                                <li>根茎类：阴凉干燥处存放</li>
                                <li>水果：按品种分类，避免混放</li>
                                <li>豆制品：0-4℃冷藏，密封保存</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="storage-category p-3 border rounded">
                            <h6 class="text-warning"><i class="fas fa-egg"></i> 其他食材</h6>
                            <ul class="small ps-3 mb-0">
                                <li>鸡蛋：7-10℃冷藏，避免阳光直射</li>
                                <li>调味品：阴凉干燥处，密封保存</li>
                                <li>干货：密封防潮，避免虫害</li>
                                <li>面粉/大米：通风干燥处，防潮防虫</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="storage-principles mt-3 p-3 bg-light rounded">
                    <h6 class="fw-bold"><i class="fas fa-clipboard-list"></i> 存放基本原则</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="small mb-0">
                                <li><strong>先进先出原则</strong> - 新入库的食材放在后面，先入库的放在前面</li>
                                <li><strong>分类存放原则</strong> - 不同类别的食材分开存放，避免交叉污染</li>
                                <li><strong>离地离墙原则</strong> - 食材存放架应离地10cm以上，离墙10cm以上</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="small mb-0">
                                <li><strong>标识清晰原则</strong> - 所有食材应有明确标签，包括名称、入库日期、保质期</li>
                                <li><strong>生熟分开原则</strong> - 生食和熟食严格分开存放，避免交叉污染</li>
                                <li><strong>温度控制原则</strong> - 严格按照食材要求的温度范围存放</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库后操作提示 -->
    <div class="card mb-4 border-info">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0 d-flex justify-content-between align-items-center">
                <span><i class="fas fa-clipboard-list"></i> 入库后操作清单</span>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#postStorageGuide">
                    展开/收起
                </button>
            </h5>
        </div>
        <div class="collapse" id="postStorageGuide">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="post-storage-task p-3 border rounded mb-3">
                            <h6><i class="fas fa-print"></i> 打印入库单据</h6>
                            <p class="small text-muted">打印入库单据并由相关人员签字确认，存档保存</p>
                            <button class="btn btn-sm btn-outline-primary" id="printStorageDocBtn" disabled>
                                <i class="fas fa-print"></i> 打印入库单
                            </button>
                        </div>
                        <div class="post-storage-task p-3 border rounded mb-3">
                            <h6><i class="fas fa-tags"></i> 标签打印</h6>
                            <p class="small text-muted">为大包装食材打印标签，包含名称、入库日期、保质期等信息</p>
                            <button class="btn btn-sm btn-outline-primary" id="printLabelsBtn" disabled>
                                <i class="fas fa-tags"></i> 打印标签
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="post-storage-task p-3 border rounded mb-3">
                            <h6><i class="fas fa-file-invoice-dollar"></i> 财务记账</h6>
                            <p class="small text-muted">将入库信息提交给财务部门，进行成本核算</p>
                            <button class="btn btn-sm btn-outline-primary" id="sendToFinanceBtn" disabled>
                                <i class="fas fa-paper-plane"></i> 发送至财务
                            </button>
                        </div>
                        <div class="post-storage-task p-3 border rounded mb-3">
                            <h6><i class="fas fa-chart-pie"></i> 库存报告</h6>
                            <p class="small text-muted">查看更新后的库存报告，确认入库数据已正确反映</p>
                            <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-chart-pie"></i> 查看库存报告
                            </a>
                        </div>
                    </div>
                </div>
                <div class="alert alert-success mt-2 mb-0">
                    <i class="fas fa-check-circle"></i> <strong>完成提示：</strong> 入库操作完成后，系统将自动更新库存数量。请确保完成上述后续操作，以确保食材管理的完整性和可追溯性。
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 采购订单预览模态框 -->
<div class="modal fade" id="orderPreviewModal" tabindex="-1" role="dialog" aria-labelledby="orderPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="orderPreviewModalLabel"><i class="fas fa-file-alt"></i> 采购订单预览</h5>
                <button type="button" class="close text-white" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="orderPreviewContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载订单信息...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="#" id="selectOrderBtn" class="btn btn-primary">
                    <i class="fas fa-check"></i> 选择此订单
                </a>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 初始化文件上传控件
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);

        // 更新上传状态
        var statusBadge = $(this).closest('.document-upload-box').find('.document-status .badge');
        statusBadge.removeClass('badge-danger').addClass('badge-success').text('已上传');
    });

    // 检查表单验证
    $('#preCheckList input[type="checkbox"]').on('change', function() {
        validateChecklist();
    });

    function validateChecklist() {
        var allChecked = true;
        $('#preCheckList input[type="checkbox"]').each(function() {
            if (!$(this).prop('checked')) {
                allChecked = false;
                return false;
            }
        });

        // 如果所有检查项都已勾选，启用提交按钮
        if (allChecked) {
            $('button[type="submit"]').prop('disabled', false);
        } else {
            $('button[type="submit"]').prop('disabled', true);
        }
    }

    // 显示直接创建表单
    function showDirectCreateForm() {
        $('#directCreateFormContainer').show();
        $('#purchaseOrderListContainer').hide();
    }

    // 显示采购订单列表
    function showPurchaseOrderList() {
        $('#purchaseOrderListContainer').show();
        $('#directCreateFormContainer').hide();
    }

    // 隐藏采购订单列表
    function hidePurchaseOrderList() {
        $('#purchaseOrderListContainer').hide();
    }

    // 页面加载时禁用提交按钮，直到所有检查项都已勾选
    $(document).ready(function() {
        $('button[type="submit"]').prop('disabled', true);

        // 设置当前日期为默认值
        if ($('#stock_in_date').val() === '') {
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            today = yyyy + '-' + mm + '-' + dd;
            $('#stock_in_date').val(today);
        }

        // 搜索功能
        $('#orderSearchInput').on('keyup', function() {
            const searchText = $(this).val().toLowerCase();
            $('#purchaseOrderTable tbody tr').each(function() {
                const orderNumber = $(this).find('td:first-child').text().toLowerCase();
                const supplier = $(this).find('td:nth-child(2)').text().toLowerCase();

                if (orderNumber.includes(searchText) || supplier.includes(searchText)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // 状态筛选
        $('.btn-group button[data-filter]').on('click', function() {
            const filter = $(this).data('filter');

            // 更新按钮状态
            $('.btn-group button[data-filter]').removeClass('active');
            $(this).addClass('active');

            // 筛选表格
            if (filter === 'all') {
                $('#purchaseOrderTable tbody tr').show();
            } else {
                $('#purchaseOrderTable tbody tr').hide();
                $('#purchaseOrderTable tbody tr[data-status="' + filter + '"]').show();
            }
        });

        // 排序功能
        let dateSort = 'desc';
        $('#sortByDate').on('click', function() {
            const rows = $('#purchaseOrderTable tbody tr').get();
            rows.sort(function(a, b) {
                const dateA = new Date($(a).find('td:nth-child(3)').text());
                const dateB = new Date($(b).find('td:nth-child(3)').text());

                if (dateSort === 'asc') {
                    return dateA - dateB;
                } else {
                    return dateB - dateA;
                }
            });

            dateSort = dateSort === 'asc' ? 'desc' : 'asc';
            $(this).find('i').toggleClass('fa-sort-up fa-sort-down');

            $.each(rows, function(index, row) {
                $('#purchaseOrderTable tbody').append(row);
            });
        });

        let supplierSort = 'asc';
        $('#sortBySupplier').on('click', function() {
            const rows = $('#purchaseOrderTable tbody tr').get();
            rows.sort(function(a, b) {
                const supplierA = $(a).find('td:nth-child(2)').text().trim();
                const supplierB = $(b).find('td:nth-child(2)').text().trim();

                if (supplierSort === 'asc') {
                    return supplierA.localeCompare(supplierB);
                } else {
                    return supplierB.localeCompare(supplierA);
                }
            });

            supplierSort = supplierSort === 'asc' ? 'desc' : 'asc';
            $(this).find('i').toggleClass('fa-sort-up fa-sort-down');

            $.each(rows, function(index, row) {
                $('#purchaseOrderTable tbody').append(row);
            });
        });

        // 预览功能
        $('.preview-btn').on('click', function() {
            const orderId = $(this).data('id');
            $('#selectOrderBtn').attr('href', "{{ url_for('stock_in.create_from_purchase', purchase_order_id=0) }}".replace('0', orderId));

            // 显示模态框
            $('#orderPreviewModal').modal('show');

            // 加载订单详情
            $.ajax({
                url: '/purchase-order/' + orderId + '/json',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const order = response.data;
                        let html = `
                            <div class="order-preview">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h5>订单信息</h5>
                                        <table class="table table-sm">
                                            <tr>
                                                <th width="30%">订单编号</th>
                                                <td>${order.order_number}</td>
                                            </tr>
                                            <tr>
                                                <th>供应商</th>
                                                <td>${order.supplier_name || '自购'}</td>
                                            </tr>
                                            <tr>
                                                <th>订单日期</th>
                                                <td>${order.order_date}</td>
                                            </tr>
                                            <tr>
                                                <th>状态</th>
                                                <td><span class="badge badge-${order.status === '已确认' ? 'success' : 'warning'}">${order.status}</span></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>订单摘要</h5>
                                        <table class="table table-sm">
                                            <tr>
                                                <th width="30%">食材种类</th>
                                                <td>${order.items.length}种</td>
                                            </tr>
                                            <tr>
                                                <th>总金额</th>
                                                <td>¥${parseFloat(order.total_amount).toFixed(2)}</td>
                                            </tr>
                                            <tr>
                                                <th>创建人</th>
                                                <td>${order.creator_name || '未知'}</td>
                                            </tr>
                                            <tr>
                                                <th>备注</th>
                                                <td>${order.notes || '无'}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <h5>订单明细</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>序号</th>
                                                <th>食材名称</th>
                                                <th>数量</th>
                                                <th>单位</th>
                                                <th>单价</th>
                                                <th>金额</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                        `;

                        order.items.forEach((item, index) => {
                            const unitPrice = parseFloat(item.unit_price || 0);
                            const quantity = parseFloat(item.quantity || 0);
                            html += `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${item.ingredient_name}</td>
                                    <td>${quantity}</td>
                                    <td>${item.unit}</td>
                                    <td>¥${unitPrice.toFixed(2)}</td>
                                    <td>¥${(quantity * unitPrice).toFixed(2)}</td>
                                </tr>
                            `;
                        });

                        html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;

                        $('#orderPreviewContent').html(html);
                    } else {
                        $('#orderPreviewContent').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> 加载订单信息失败: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#orderPreviewContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> 网络错误，无法加载订单信息
                        </div>
                    `);
                }
            });
        });
    });
</script>
{% endblock %}

{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>